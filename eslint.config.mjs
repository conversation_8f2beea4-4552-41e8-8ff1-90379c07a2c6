import js from '@eslint/js';
import { defineConfig } from 'eslint/config';
import simpleImportSort from 'eslint-plugin-simple-import-sort';
import globals from 'globals';

export default defineConfig([
	{
		files: ['**/*.{js,mjs,cjs}'],
		plugins: {
			js,
			'simple-import-sort': simpleImportSort,
		},
		extends: ['js/recommended'],
		rules: {
			'simple-import-sort/imports': 'error',
			'simple-import-sort/exports': 'error',
		},
	},
	{ files: ['**/*.{js,mjs,cjs}'], languageOptions: { globals: globals.node } },
	{ ignores: ['.wrangler/'] },
]);
