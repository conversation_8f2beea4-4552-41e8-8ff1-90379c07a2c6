import { ALLOWED_FILE_TYPES } from '../constants/index.js';
import { getMediaGroup } from '../redis.js';
import { fetchAndProcessFile } from './fileProcessor.js';

/**
 * Processes attachments from a Telegram message, handling various attachment scenarios.
 *
 * This function handles multiple attachment sources in priority order:
 * 1. Replied-to media (photos or documents)
 * 2. Media groups (albums of photos)
 * 3. Current message attachments (photos or documents)
 *
 * For media groups, a delay is implemented to ensure all media in the group has been received
 * by the server before processing.
 *
 * @param {object} env - Environment variables containing configuration like bot token
 * @param {object} messageData - The Telegram message data object containing attachment information
 * @returns {Promise<Array<object>>} A promise that resolves to an array of processed attachment objects
 *
 * @example
 * const attachments = await processAttachments(env, messageData);
 * // Returns array of processed attachments with content and metadata
 */
export async function processAttachments(env, messageData) {
	const { replyToPhoto, replyToDocument, mediaGroupId, photo, document } = messageData;
	const botToken = env.HRMNY_BOT_TOKEN;
	let attachmentsToProcess = [];

	// <PERSON>le replied-to media (highest priority)
	if (replyToPhoto && replyToPhoto.length > 0) {
		console.log('Processing replied-to photo...');
		// Telegram returns photos in increasing order of resolution
		// The last photo in the array has the highest resolution (1080px)
		const highestResPhoto = replyToPhoto[replyToPhoto.length - 1];
		attachmentsToProcess.push({
			file_id: highestResPhoto.file_id,
			mime_type: 'image/jpeg',
		});
	} else if (replyToDocument && replyToDocument.file_id) {
		console.log('Processing replied-to document...');
		// For documents, use the provided file ID and MIME type
		attachmentsToProcess.push({
			file_id: replyToDocument.file_id,
			mime_type: replyToDocument.mime_type,
		});
	} else if (mediaGroupId) {
		console.log(`Processing media group with ID: ${mediaGroupId}`);
		// Add delay to ensure all media in the group has been received by Telegram
		// Media groups are sent as separate messages that arrive sequentially
		// This delay ensures we capture all items in the group
		await new Promise((resolve) => setTimeout(resolve, 1500));

		try {
			// Retrieve all media items in the group from Redis storage
			const mediaGroupResult = await getMediaGroup(env, mediaGroupId);

			if (Array.isArray(mediaGroupResult)) {
				// Convert media group items to attachment format
				// For photos in media groups, set MIME type to image/jpeg
				// For other types, preserve the original MIME type
				attachmentsToProcess = mediaGroupResult.map((item) => ({
					file_id: item.file_id,
					mime_type: item.type === 'photo' ? 'image/jpeg' : item.mime_type,
				}));
			}
		} catch (e) {
			console.error(`Error getting media group ${mediaGroupId}:`, e);
		}
	} else if (photo && photo.length > 0) {
		console.log('Processing photo from current message...');
		// Telegram returns photos in increasing order of resolution
		// The last photo in the array has the highest resolution (1080px)
		const highestResPhoto = photo[photo.length - 1];
		attachmentsToProcess.push({
			file_id: highestResPhoto.file_id,
			mime_type: 'image/jpeg',
		});
	} else if (document && document.file_id) {
		console.log('Processing document from current message...');
		// For documents, use the provided file ID and MIME type
		attachmentsToProcess.push({
			file_id: document.file_id,
			mime_type: document.mime_type,
		});
	}

	// If no attachments were found in any of the sources, return empty array
	if (attachmentsToProcess.length === 0) {
		return [];
	}

	// Create promises to fetch and process each attachment
	// This allows for concurrent processing of multiple attachments
	const processPromises = attachmentsToProcess.map((attachment) =>
		fetchAndProcessFile(botToken, attachment.file_id, attachment.mime_type, ALLOWED_FILE_TYPES),
	);

	// Wait for all attachments to be processed
	// Promise.all ensures we wait for all promises to resolve (or reject)
	const processedAttachments = await Promise.all(processPromises);

	// Filter out any null values (failed processing) and return valid attachments
	// This ensures we only return successfully processed attachments
	return processedAttachments.filter(Boolean);
}
