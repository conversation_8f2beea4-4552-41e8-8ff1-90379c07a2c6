/**
 * @fileoverview Handles Telegram callback queries from inline keyboards
 * Processes interactive button presses from reminder messages and other UI elements
 */

import { sendLongTelegramMessage } from '../chat/telegram/index.js';
import { getRedisClient } from '../redis/redisClient.js';
import { TaskManager } from './tasks/taskManager.js';
import { escapeHtml } from './telegramUtils.js';
import { createTaskTools } from './tools/taskTools.js';

/**
 * Main callback query handler
 * Routes different callback types to appropriate handlers
 */
export async function handleCallbackQuery(env, callbackQuery) {
	const data = callbackQuery.data;
	const userId = callbackQuery.from.id;
	const chatId = callbackQuery.message.chat.id;
	const messageId = callbackQuery.message.message_id;

	console.log(`Processing callback query: ${data} from user ${userId}`);

	try {
		// Answer the callback query immediately to remove loading state
		await answerCallbackQuery(env, callbackQuery.id);

		// Route to appropriate handler based on callback data
		if (data.startsWith('complete_')) {
			await handleTaskComplete(env, data, userId, chatId, messageId);
		} else if (data.startsWith('snooze_')) {
			await handleTaskSnooze(env, data, userId, chatId, messageId);
		} else if (data === 'list_tasks') {
			await handleListTasks(env, userId, chatId);
		} else if (data === 'reminder_settings') {
			await handleReminderSettings(env, userId, chatId);
		} else {
			console.warn(`Unknown callback data: ${data}`);
			await sendLongTelegramMessage(env, chatId, '❌ Unknown action. Please try again.', { parseMode: 'HTML' });
		}
	} catch (error) {
		console.error('Error handling callback query:', error);
		await sendLongTelegramMessage(env, chatId, '❌ Sorry, something went wrong processing that action.', { parseMode: 'HTML' });
	}
}

/**
 * Handle task completion callback
 */
async function handleTaskComplete(env, data, userId, chatId, messageId) {
	try {
		const taskId = data.replace('complete_', '');

		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);

		const task = await taskManager.updateTaskStatus(userId, taskId, 'completed');

		if (task) {
			// Edit the original message to show completion
			await editMessageText(
				env,
				chatId,
				messageId,
				`✅ <b>Task Completed</b>\n\n<s>${escapeHtml(task.description)}</s>\n\n<i>Completed at ${new Date().toLocaleString()}</i>`,
				{ parse_mode: 'HTML' },
			);

			// Send confirmation message
			await sendLongTelegramMessage(
				env,
				chatId,
				`🎉 Great job! Task "${escapeHtml(task.description)}" has been marked as completed.`,
				{
					parseMode: 'HTML',
				},
			);

			console.log(`Task ${taskId} completed by user ${userId} via callback`);
		} else {
			await sendLongTelegramMessage(env, chatId, '❌ Task not found or already completed.', { parseMode: 'HTML' });
		}
	} catch (error) {
		console.error('Error completing task via callback:', error);
		await sendLongTelegramMessage(env, chatId, '❌ Failed to complete the task. Please try again.', { parseMode: 'HTML' });
	}
}

/**
 * Handle task snooze callback
 */
async function handleTaskSnooze(env, data, userId, chatId, messageId) {
	try {
		const [, taskId, minutes] = data.split('_');
		const snoozeMinutes = parseInt(minutes);

		const redisClient = getRedisClient(env);
		const taskManager = new TaskManager(redisClient);

		const task = await taskManager.snoozeTaskReminder(userId, taskId, snoozeMinutes);

		if (task) {
			const hours = Math.floor(snoozeMinutes / 60);
			const timeUnit = hours >= 1 ? `${hours} hour${hours > 1 ? 's' : ''}` : `${snoozeMinutes} minutes`;

			// Edit the original message to show snooze status
			await editMessageText(
				env,
				chatId,
				messageId,
				`⏰ <b>Task Snoozed</b>\n\n${escapeHtml(task.description)}\n\n<i>Snoozed for ${escapeHtml(
					timeUnit,
				)}</i>\n<i>Will remind again at ${escapeHtml(new Date(task.reminderSettings.snoozeUntil).toLocaleString())}</i>`,
				{ parse_mode: 'HTML' },
			);

			// Send confirmation message
			await sendLongTelegramMessage(env, chatId, `⏰ Task snoozed for ${escapeHtml(timeUnit)}. I'll remind you again later.`, {
				parseMode: 'HTML',
			});

			console.log(`Task ${taskId} snoozed for ${snoozeMinutes} minutes by user ${userId}`);
		} else {
			await sendLongTelegramMessage(env, chatId, '❌ Task not found or could not be snoozed.', { parseMode: 'HTML' });
		}
	} catch (error) {
		console.error('Error snoozing task via callback:', error);
		await sendLongTelegramMessage(env, chatId, '❌ Failed to snooze the task. Please try again.', { parseMode: 'HTML' });
	}
}

/**
 * Handle list tasks callback
 */
async function handleListTasks(env, userId, chatId) {
	try {
		// Use the existing list tasks tool
		const taskTools = createTaskTools(env, userId);
		const listTool = taskTools[1]; // _listTasksTool is at index 1
		const result = await listTool.func();

		await sendLongTelegramMessage(env, chatId, result, { parseMode: 'HTML' });
	} catch (error) {
		console.error('Error listing tasks via callback:', error);
		await sendLongTelegramMessage(env, chatId, '❌ Failed to retrieve your tasks. Please try again.', { parseMode: 'HTML' });
	}
}

/**
 * Handle reminder settings callback
 */
async function handleReminderSettings(env, userId, chatId) {
	try {
		// Use the existing reminder settings tool
		const taskTools = createTaskTools(env, userId);
		const settingsTool = taskTools[4]; // _reminderSettingsTool is at index 4
		const result = await settingsTool.func({ action: 'view' });

		await sendLongTelegramMessage(env, chatId, result, { parseMode: 'HTML' });
	} catch (error) {
		console.error('Error showing reminder settings via callback:', error);
		await sendLongTelegramMessage(env, chatId, '❌ Failed to retrieve your reminder settings. Please try again.', {
			parseMode: 'HTML',
		});
	}
}

/**
 * Answer callback query to remove loading state
 */
async function answerCallbackQuery(env, callbackQueryId, text = '') {
	const botToken = env.HRMNY_BOT_TOKEN;

	if (!botToken) {
		console.error('Telegram Bot Token is missing for callback query answer.');
		return;
	}

	const url = `https://api.telegram.org/bot${botToken}/answerCallbackQuery`;

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				callback_query_id: callbackQueryId,
				text: text,
				show_alert: false,
			}),
		});

		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(`Failed to answer callback query: ${response.status} - ${result.description || 'Unknown error'}`);
		}
	} catch (error) {
		console.error('Error answering callback query:', error);
	}
}

/**
 * Edit message text (for updating reminder messages)
 */
async function editMessageText(env, chatId, messageId, text, options = {}) {
	const botToken = env.HRMNY_BOT_TOKEN;

	if (!botToken) {
		console.error('Telegram Bot Token is missing for message edit.');
		return;
	}

	const url = `https://api.telegram.org/bot${botToken}/editMessageText`;

	const messageData = {
		chat_id: chatId,
		message_id: messageId,
		text: text,
		parse_mode: options.parse_mode || 'HTML',
		...options,
	};

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(messageData),
		});

		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(`Failed to edit message: ${response.status} - ${result.description || 'Unknown error'}`);
		}
	} catch (error) {
		console.error('Error editing message:', error);
	}
}

/**
 * Check if webhook data contains a callback query
 */
export function hasCallbackQuery(webhookData) {
	return !!(webhookData && webhookData.callback_query);
}

/**
 * Extract callback query from webhook data
 */
export function extractCallbackQuery(webhookData) {
	if (!hasCallbackQuery(webhookData)) {
		return null;
	}

	return webhookData.callback_query;
}
