import { PostgresSaver } from '@langchain/langgraph-checkpoint-postgres';

import { deleteChatHistory } from '../../../redis.js';
import { deleteChatEmbeddings } from '../../../vectorStore.js';
import { sendLongTelegramMessage } from '../../telegram/index.js';
import { escapeHtml } from '../../telegramUtils.js';

/**
 * Handles the clear history command by deleting all chat-related data.
 *
 * This function performs a comprehensive cleanup of a chat's data by:
 * 1. Removing chat history from Redis storage
 * 2. Deleting chat embeddings from the vector store
 *
 * The function implements a robust error handling strategy where each deletion
 * operation is attempted independently. Even if one operation fails, others
 * will still be attempted, and the user receives detailed feedback about
 * the success status of each operation.
 *
 * @param {Object} env - Environment variables containing configuration and secrets
 * @param {string} chatId - The ID of the chat whose history should be cleared
 * @param {string} userId - The ID of the user requesting the action
 * @param {string} botUsername - The username of the bot (used for Redis key construction)
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 *
 * @example
 * const success = await handleClearHistoryCommand(env, '12345', '67890', 'mybot');
 */
export async function handleClearHistoryCommand(env, chatId, userId, botUsername) {
	try {
		console.log(`[handleClearHistoryCommand] Processing clear history command for chat ${chatId}, user ${userId}`);
		console.log(`[handleClearHistoryCommand] Bot username: "${botUsername}"`);
		console.log(`[handleClearHistoryCommand] Environment bot username: "${env.TELEGRAM_BOT_USERNAME}"`);

		// Object to track the success status of each deletion operation
		// This allows for independent error handling and detailed user feedback
		const deletionResults = {
			// Whether Redis chat history was successfully deleted
			redis: false,
			// Whether vector store embeddings were successfully deleted
			// This operation may take longer for chats with extensive history
			embeddings: false,
		};

		// Delete thread from Postgres
		try {
			console.log(`[handleClearHistoryCommand] Starting Postgres thread deletion...`);
			const checkpointer = await PostgresSaver.fromConnString(env.SUPABASE_URL);
			await checkpointer.deleteThread(chatId);
			console.log(`[handleClearHistoryCommand] Postgres thread deletion completed for chat ${chatId}.`);
			deletionResults.postgres = true;
		} catch (error) {
			console.error(`[handleClearHistoryCommand] Error deleting Postgres thread for chat ${chatId}:`, error);
			deletionResults.postgres = false;
		}

		// Delete chat history from Redis storage
		// This removes the conversation messages and metadata
		try {
			console.log(`[handleClearHistoryCommand] Starting Redis chat history deletion...`);
			// The deleteChatHistory function returns a boolean indicating success
			deletionResults.redis = await deleteChatHistory(env, chatId, botUsername);
			console.log(`[handleClearHistoryCommand] Redis chat history deletion result: ${deletionResults.redis}`);
		} catch (error) {
			// Log the error for debugging but continue with other operations
			// This ensures that a failure in one storage system doesn't prevent
			// cleanup in other systems
			console.error(`[handleClearHistoryCommand] Error deleting Redis chat history for chat ${chatId}:`, error);
			deletionResults.redis = false;
		}

		// Delete chat embeddings from the vector store
		// This removes the AI-processed representations of the chat history
		// which are used for semantic search and context retrieval
		// Note: This operation may take longer for chats with extensive history
		// as it involves potentially multiple database operations
		try {
			console.log(`[handleClearHistoryCommand] Starting vector store cleanup for chat ${chatId}...`);
			// The deleteChatEmbeddings function returns a boolean indicating success
			deletionResults.embeddings = await deleteChatEmbeddings(env, chatId);
			console.log(
				`[handleClearHistoryCommand] Vector store cleanup completed for chat ${chatId}. Result: ${deletionResults.embeddings}`,
			);
		} catch (error) {
			// Log the error for debugging but continue execution
			// This follows the principle of graceful degradation -
			// even if one data store fails, we attempt to clean up others
			console.error(`[handleClearHistoryCommand] Error deleting chat embeddings for chat ${chatId}:`, error);
			deletionResults.embeddings = false;
		}

		// Prepare user-facing response message based on operation results
		// The response provides detailed feedback about what was successfully
		// deleted and what might have failed
		let responseMessage;

		// Check if all deletion operations were successful
		const allSuccessful = deletionResults.postgres && deletionResults.redis && deletionResults.embeddings;
		// Check if at least one deletion operation was successful
		const anySuccessful = deletionResults.postgres || deletionResults.redis || deletionResults.embeddings;

		// Count successful operations to provide detailed feedback
		const successCount = Object.values(deletionResults).filter((result) => result === true).length;
		const totalOperations = Object.keys(deletionResults).length;

		if (allSuccessful) {
			// All data stores were successfully cleared
			responseMessage = `✅ All data cleared successfully! We can start completely fresh! (${successCount}/${totalOperations} operations completed)`;
		} else if (anySuccessful) {
			// Some data was cleared but not all - provide guidance for next steps
			responseMessage = `⚠️ Partial data cleared. ${successCount}/${totalOperations} operations completed successfully. Some items could not be deleted. Try again or contact support.`;
		} else {
			// All deletion operations failed - suggest retrying
			responseMessage = `❌ Failed to clear any data. Please try again later.`;
		}

		// Send the response message to the user via Telegram
		// The message is formatted for HTML parsing
		const formattedResponse = escapeHtml(responseMessage);
		await sendLongTelegramMessage(env, chatId, formattedResponse, { parseMode: 'HTML' });

		// Log completion with detailed results for monitoring and debugging
		console.log(`Clear history command completed for chat ${chatId}. Results:`, deletionResults);
		return true;
	} catch (error) {
		// Handle any unexpected errors that occurred during command processing
		console.error(`Error handling clear history command for chat ${chatId}:`, error);

		// Send user-friendly error message to the user
		// This ensures the user is informed even if something goes wrong
		const errorMessage = `❌ Error clearing chat history. Please try again later.`;

		try {
			// Attempt to send the error message
			const formattedErrorResponse = escapeHtml(errorMessage);
			await sendLongTelegramMessage(env, chatId, formattedErrorResponse, { parseMode: 'HTML' });
		} catch (sendError) {
			// If we can't even send the error message, log it for debugging
			console.error(`Failed to send error message for clear history command:`, sendError);
		}

		// Return false to indicate command processing failed
		return false;
	}
}
