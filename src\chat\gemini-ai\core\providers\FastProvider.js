/**
 * @fileoverview
 * Provides a fast and resilient interface for generating text completions by leveraging
 * multiple AI providers with a fallback mechanism. It prioritizes speed and reliability
 * by trying a preferred provider first and then falling back to others if the attempt fails.
 */

import { CIRCUIT_BREAKER_CONFIGS, getCircuitBreaker } from '../../../../utils/CircuitBreaker.js';
import { CEREBRAS_BASE_URL, CEREBRAS_MODELS, GROQ_BASE_URL, GROQ_MODELS, OPENAI_BASE_URL, OPENAI_MODELS } from '../../config/constants.js';
import { ModelFactory } from '../../factories/ModelFactory.js';
import { langfuseManager } from '../../utils/LangfuseManager.js';
import { MessageConverter } from '../../utils/MessageConverter.js';

/**
 * Manages multiple AI providers to ensure fast and reliable text completions.
 * It attempts to use a preferred provider and includes a fallback mechanism to other configured providers.
 */
export class FastProvider {
	/**
	 * Initializes a new instance of the FastProvider.
	 */
	constructor() {
		/**
		 * @type {Object|null}
		 * @private
		 * Caches provider configurations to avoid redundant setup.
		 */
		this._providerConfigCache = null;
	}

	/**
	 * Retrieves and caches the configurations for all available providers.
	 * It initializes the cache on the first call and updates API keys from the environment on subsequent calls.
	 * @param {object} env - The environment variables.
	 * @returns {object} The configurations for all providers.
	 * @private
	 */
	_getProviderConfigurations(env) {
		if (!this._providerConfigCache) {
			this._providerConfigCache = {
				groq: { baseUrl: GROQ_BASE_URL, models: GROQ_MODELS, apiKey: env.GROQ_API_KEY },
				cerebras: { baseUrl: CEREBRAS_BASE_URL, models: CEREBRAS_MODELS, apiKey: env.CEREBRAS_API_KEY },
				openai: { baseUrl: OPENAI_BASE_URL, models: OPENAI_MODELS, apiKey: env.OPENAI_API_KEY },
			};
		}
		// Ensure API keys are always up-to-date from the environment
		this._providerConfigCache.groq.apiKey = env.GROQ_API_KEY;
		this._providerConfigCache.cerebras.apiKey = env.CEREBRAS_API_KEY;
		this._providerConfigCache.openai.apiKey = env.OPENAI_API_KEY;

		return this._providerConfigCache;
	}

	/**
	 * Attempts to generate a text completion by trying a series of providers in a prioritized order.
	 * It starts with the preferred provider and falls back to others upon failure.
	 * @param {object} env - The environment variables.
	 * @param {object} config - The configuration for the completion request.
	 * @param {Array<object>} contents - The message history or content for the completion.
	 * @param {ErrorAggregator} errorAggregator - An aggregator to collect errors from different providers.
	 * @returns {Promise<object|null>} The completion result or null if all providers fail.
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		const langfuseHandler = langfuseManager.getHandler(env, config);

		const providers = this._getProviderConfigurations(env);
		const preferredProvider = config.inferenceProvider || 'cerebras'; // Default to Cerebras if not specified

		console.log(`Preferred inference provider: ${preferredProvider}`);

		const providerOrder = this._getProviderPriorityOrder(preferredProvider, providers);
		console.log(`Provider retry order: ${providerOrder.join(' -> ')}`);

		// Iterate through providers and attempt to get a completion
		for (const providerName of providerOrder) {
			const provider = providers[providerName];

			if (!this._isProviderConfigured(provider)) {
				const errorMsg = `Missing configuration for provider: ${providerName}`;
				console.warn(errorMsg);
				errorAggregator.addError(providerName, new Error(errorMsg));
				continue; // Skip to the next provider
			}

			console.log(`Trying provider: ${providerName}`);

			const providerConfig = {
				...config,
				baseUrl: provider.baseUrl,
				apiKey: provider.apiKey,
			};

			// Get the list of models for the provider, handling potential whitespace and empty entries
			let modelNames = provider.models
				.split(',')
				.map((m) => m.trim())
				.filter(Boolean);

			// If a specific model is requested and available for this provider, try it first.
			if (config.model && modelNames.includes(config.model)) {
				modelNames = [config.model, ...modelNames.filter((m) => m !== config.model)];
			}

			// Try only the first model, fallback to next provider instead
			const modelName = modelNames[0];
			if (modelName) {
				const circuitBreaker = getCircuitBreaker(`fast_provider_${providerName}`, CIRCUIT_BREAKER_CONFIGS.FAST_PROVIDERS);

				// Check if circuit breaker is open
				if (circuitBreaker.isOpen()) {
					console.warn(`[FastProvider] Circuit breaker is open for ${providerName}, skipping`);
					errorAggregator.addError(providerName, new Error('Circuit breaker is open'));
					continue;
				}

				try {
					const result = await circuitBreaker.execute(async () => {
						return await this._attemptModelCompletion(modelName, providerConfig, contents, langfuseHandler);
					});

					return result; // Return the successful result immediately
				} catch (error) {
					const context = `${providerName}/${modelName}`;
					console.error(`${context} failed:`, error);
					errorAggregator.addError(context, error);
					// Continue to next provider instead of trying more models
				}
			}
		}

		// Return null if all providers and their models fail
		return null;
	}

	/**
	 * Checks if a provider has the necessary configuration (baseUrl and apiKey) to be used.
	 * @param {object} provider - The provider configuration object.
	 * @returns {boolean} True if the provider is configured, false otherwise.
	 * @private
	 */
	_isProviderConfigured(provider) {
		return provider && provider.baseUrl && provider.apiKey;
	}

	/**
	 * Determines the order in which to try providers, starting with the preferred one.
	 * @param {string} preferredProvider - The name of the preferred provider.
	 * @param {object} availableProviders - A map of all configured providers.
	 * @returns {Array<string>} A sorted list of provider names.
	 * @private
	 */
	_getProviderPriorityOrder(preferredProvider, availableProviders) {
		const allProviders = Object.keys(availableProviders);
		const priorityOrder = [];

		// Add the preferred provider to the front of the list if it's valid
		if (preferredProvider && allProviders.includes(preferredProvider)) {
			priorityOrder.push(preferredProvider);
		}

		// Add the rest of the providers
		allProviders.forEach((provider) => {
			if (provider !== preferredProvider) {
				priorityOrder.push(provider);
			}
		});

		return priorityOrder;
	}

	/**
	 * Attempts a single chat completion with a specific model from a provider.
	 * @param {string} modelName - The name of the model to use.
	 * @param {object} config - The configuration for the model.
	 * @param {Array<object>} contents - The message content for the completion.
	 * @param {object} langfuseHandler - The handler for logging to Langfuse.
	 * @returns {Promise<object>} The result from the model.
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, contents, langfuseHandler) {
		console.log(`Attempting generation with model: ${modelName}`);

		const model = ModelFactory.createFastModel(modelName, config);
		const messages = MessageConverter.convertToLangChainMessages(contents, config.systemInstruction);

		const invokeOptions = {};
		if (langfuseHandler) {
			invokeOptions.callbacks = [langfuseHandler];
		}

		const response = await model.invoke(messages, invokeOptions);
		console.log(`✅ Successfully completed ${config.traceTags.join(', ')} with model: ${modelName}`);

		// Clean up the response by removing any thought tags.
		const cleanedContent = response.content.replace(/<think>[\s\S]*?<\/think>/gi, '');

		return {
			text: cleanedContent,
		};
	}
}
