/**
 * @fileoverview Main entry point for the refactored GeminiAI module.
 *
 * This barrel module serves as the public API for the Gemini AI integration,
 * providing a clean and organized interface for external consumers. It re-exports
 * all necessary components from the internal module structure, following a logical
 * grouping strategy for easier discovery and use.
 *
 * The module architecture is designed to:
 * - Provide a single import point for all Gemini AI functionality
 * - Maintain separation of concerns through internal modular design
 * - Enable easy extension and maintenance of individual components
 * - Support dependency injection and testing through well-defined interfaces
 *
 * Key features include:
 * - API key management with rotation, caching, and rate limit handling
 * - Model factory pattern for creating different types of AI models
 * - Message conversion utilities for LangChain compatibility
 * - Embedding generation with comprehensive error handling
 * - Custom error classes for precise error reporting and handling
 * - Support for multiple AI providers with configurable priority order
 */

// === Configuration Constants ===
// Export all configuration constants used throughout the Gemini AI module
// These include API endpoints, rate limits, timeout values, and other
// configurable parameters that control the behavior of the AI integration
export * from './config/constants.js';

// === Error Handling ===
// Export all custom error classes for comprehensive error handling
// These provide specific error types for different failure modes in the AI pipeline
// allowing consumers to handle different error scenarios appropriately
export * from './errors/GeminiErrors.js';

// === Service Managers ===
// Export core service managers that handle specific aspects of the AI integration
// Each manager encapsulates related functionality and state management
export { EmbeddingManager } from './managers/EmbeddingManager.js'; // Manages embedding generation and caching
export { SimpleApiKeyManager as ApiKeyManager } from './managers/SimpleApiKeyManager.js'; // Handles API key rotation, validation, and caching

// === Utilities ===
// Export helper utilities that provide cross-cutting functionality
// These are stateless functions that can be used across different parts of the system
export { MessageConverter } from './utils/MessageConverter.js'; // Converts between different message formats for LangChain compatibility

// === Factories ===
// Export factory classes that create instances of AI models and providers
// Factories encapsulate the complex instantiation logic and configuration
export { ModelFactory } from './factories/ModelFactory.js'; // Creates configured AI model instances based on type and parameters

// === Core Functionality ===
// Export the primary functions and classes that constitute the main API
// These represent the essential operations that consumers will use directly
export {
	callFastGenerativeAI, // Function to call a faster, potentially less capable generative AI model
	callGenerativeAI, // Function to call the primary generative AI model
	GeminiAI, // Main AI service class that coordinates all operations
	generateEmbedding, // Function to generate embeddings for text content
	getNextGeminiApiKey, // Function to retrieve the next available API key (for rotation)
	getProviderPriorityOrder, // Function to get the configured priority order of AI providers
} from './core/index.js';
