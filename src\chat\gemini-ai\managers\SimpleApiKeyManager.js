/**
 * @fileoverview Simplified API key management with basic rotation and in-memory error tracking.
 *
 * This implementation removes Redis dependency to reduce latency, using in-memory tracking instead.
 */

import { ApiKeyError } from '../errors/GeminiErrors.js';

/**
 * Manages API key rotation and error tracking with minimal overhead
 */
export class SimpleApiKeyManager {
	/**
	 * Initializes a new instance of the SimpleApiKeyManager
	 */
	constructor() {
		this.apiKeys = [];
		this.keyStates = new Map(); // In-memory key states
		this.rotationCounter = 0;
	}

	/**
	 * Loads Gemini API keys from environment variables
	 * @param {object} env - Environment variables
	 * @returns {string[]} Array of API keys
	 */
	loadGeminiApiKeys(env) {
		const keys = [];
		let i = 1;
		while (env[`GEMINI_API_KEY_${i}`]) {
			keys.push(env[`GEMINI_API_KEY_${i}`]);
			i++;
		}

		if (keys.length === 0 && env.GEMINI_API_KEY) {
			console.warn('No numbered Gemini API keys found. Falling back to single GEMINI_API_KEY from environment.');
			keys.push(env.GEMINI_API_KEY);
		}

		this.apiKeys = keys;

		// Initialize key states
		for (const key of keys) {
			if (!this.keyStates.has(key)) {
				this.keyStates.set(key, {
					isValid: true,
					errorCount: 0,
					lastError: null,
					lastErrorTime: null,
				});
			}
		}

		return keys;
	}

	/**
	 * Gets the next valid API key in rotation
	 * @param {object} env - Environment variables
	 * @returns {string} The selected API key
	 * @throws {ApiKeyError} If no valid API keys are available
	 */
	getNextGeminiApiKey(env) {
		const apiKeys = this.loadGeminiApiKeys(env);

		if (apiKeys.length === 0) {
			throw new ApiKeyError(
				'No Gemini API keys configured in environment variables (e.g., GEMINI_API_KEY_1, GEMINI_API_KEY_2, or GEMINI_API_KEY).',
			);
		}

		// If we have only one key, return it regardless of state
		if (apiKeys.length === 1) {
			return apiKeys[0];
		}

		// Try to find a valid key using round-robin
		for (let i = 0; i < apiKeys.length; i++) {
			const index = (this.rotationCounter + i) % apiKeys.length;
			const key = apiKeys[index];
			const state = this.keyStates.get(key);

			// Use key if it's marked as valid or if it's been more than 5 minutes since last error
			if (state.isValid || (state.lastErrorTime && Date.now() - state.lastErrorTime > 5 * 60 * 1000)) {
				this.rotationCounter = (index + 1) % apiKeys.length;
				console.log(`Using Gemini API key: ${key.substring(0, 10)}...`);
				return key;
			}
		}

		// If all keys are invalid, reset and try again
		for (const key of apiKeys) {
			const state = this.keyStates.get(key);
			state.isValid = true;
			state.errorCount = 0;
		}

		const key = apiKeys[this.rotationCounter];
		this.rotationCounter = (this.rotationCounter + 1) % apiKeys.length;
		console.log(`Resetting and using Gemini API key: ${key.substring(0, 10)}...`);
		return key;
	}

	/**
	 * Updates the status of an API key based on the error type
	 * @param {string} apiKey - The API key that failed
	 * @param {Error} error - The error that occurred
	 */
	updateKeyStatus(apiKey, error) {
		if (!this.keyStates.has(apiKey)) {
			return;
		}

		const state = this.keyStates.get(apiKey);
		state.lastError = error.message;
		state.lastErrorTime = Date.now();

		// For authentication errors, mark key as permanently invalid
		if (error.message && (error.message.includes('API_KEY_INVALID') || error.message.includes('unauthorized'))) {
			state.isValid = false;
			console.log(`API key ${apiKey.substring(0, 10)}... marked as invalid due to authentication error`);
		} else {
			// For other errors, increment error count
			state.errorCount++;

			// Mark as invalid if we have too many errors
			if (state.errorCount > 5) {
				state.isValid = false;
				console.log(`API key ${apiKey.substring(0, 10)}... marked as invalid due to repeated errors`);
			}
		}
	}

	/**
	 * Gets the current API key index for logging purposes
	 * @returns {number} Current API key index
	 */
	getCurrentApiKeyIndex() {
		if (this.apiKeys.length === 0) return 0;
		return this.rotationCounter === 0 ? this.apiKeys.length - 1 : (this.rotationCounter - 1) % this.apiKeys.length;
	}
}
