/**
 * @fileoverview Centralized Langfuse management utility for optimized initialization and caching
 */

import { CallbackHandler } from 'langfuse-langchain';

/**
 * Singleton manager for Langfuse handlers with advanced caching and optimization
 */
class LangfuseManager {
	constructor() {
		this._handlers = new Map(); // Cache multiple handlers by config hash
		this._lastCleanup = Date.now();
		this._cleanupInterval = 5 * 60 * 1000; // 5 minutes
		this._maxHandlers = 10; // Maximum number of cached handlers
	}

	/**
	 * Gets or creates an optimized Langfuse handler with advanced caching
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @returns {CallbackHandler|null} Langfuse handler or null if not configured
	 */
	getHandler(env, config) {
		// Check if Langfuse is properly configured
		if (!this._isLangfuseConfigured(env)) {
			return null;
		}

		// Create config hash for cache key
		const configHash = this._createConfigHash(env, config);

		// Return cached handler if available
		const cachedHandler = this._handlers.get(configHash);
		if (cachedHandler && this._isHandlerValid(cachedHandler)) {
			cachedHandler.lastUsed = Date.now();
			return cachedHandler.handler;
		}

		// Create new handler
		try {
			const handler = this._createHandler(env, config);

			// Cache the handler with metadata
			this._handlers.set(configHash, {
				handler,
				created: Date.now(),
				lastUsed: Date.now(),
				configHash,
			});

			console.log(`Langfuse handler created and cached (${this._handlers.size} total)`);
			return handler;
		} catch (error) {
			console.error('Failed to create Langfuse handler:', error);
			return null;
		}
	}

	/**
	 * Creates a new Langfuse handler with optimized configuration
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @returns {CallbackHandler} New Langfuse handler
	 * @private
	 */
	_createHandler(env, config) {
		return new CallbackHandler({
			publicKey: env.LANGFUSE_PUBLIC_KEY,
			secretKey: env.LANGFUSE_SECRET_KEY,
			baseUrl: env.LANGFUSE_BASE_URL || 'https://cloud.langfuse.com',
			environment: env.LANGFUSE_ENVIRONMENT || 'development',
			tags: config.traceTags || [],
		});
	}

	/**
	 * Checks if Langfuse is properly configured
	 * @param {object} env - Environment variables
	 * @returns {boolean} True if configured
	 * @private
	 */
	_isLangfuseConfigured(env) {
		if (!env.LANGFUSE_PUBLIC_KEY || !env.LANGFUSE_SECRET_KEY) {
			console.warn('Langfuse not configured - missing public or secret key');
			return false;
		}
		return true;
	}

	/**
	 * Creates a hash of Langfuse configuration for cache key
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @returns {string} Configuration hash
	 * @private
	 */
	_createConfigHash(env, config) {
		const configString = JSON.stringify({
			publicKey: env.LANGFUSE_PUBLIC_KEY,
			secretKey: env.LANGFUSE_SECRET_KEY,
			baseUrl: env.LANGFUSE_BASE_URL,
			environment: env.LANGFUSE_ENVIRONMENT,
			tags: config.traceTags || [],
		});

		// Simple hash function for configuration comparison
		let hash = 0;
		for (let i = 0; i < configString.length; i++) {
			const char = configString.charCodeAt(i);
			hash = (hash << 5) - hash + char;
			hash = hash & hash; // Convert to 32-bit integer
		}
		return hash.toString();
	}

	/**
	 * Checks if a cached handler is still valid
	 * @param {object} cachedHandler - Cached handler object
	 * @returns {boolean} True if valid
	 * @private
	 */
	_isHandlerValid(cachedHandler) {
		const maxAge = 30 * 60 * 1000; // 30 minutes
		const age = Date.now() - cachedHandler.created;
		return age < maxAge;
	}

	/**
	 * Safely shuts down a Langfuse handler
	 * @param {CallbackHandler} handler - Handler to shutdown
	 * @private
	 */
	async _shutdownHandler(handler) {
		try {
			await handler.shutdownAsync();
		} catch (error) {
			console.error('Error during Langfuse handler shutdown:', error);
		}
	}

	/**
	 * Cleanup all handlers - should be called on application shutdown
	 */
	async cleanup() {
		console.log(`Shutting down all Langfuse handlers (${this._handlers.size} total)`);

		const shutdownPromises = Array.from(this._handlers.values()).map((cachedHandler) => this._shutdownHandler(cachedHandler.handler));

		await Promise.allSettled(shutdownPromises);
		this._handlers.clear();

		console.log('All Langfuse handlers shutdown successfully');
	}
}

// Export singleton instance
export const langfuseManager = new LangfuseManager();
