/**
 * @fileoverview Processes incoming Telegram messages in the background.
 *
 * This module handles the complete message processing pipeline for the Telegram bot,
 * coordinating various components to provide AI-powered responses. The processing flow
 * includes:
 * 1. Message extraction and validation
 * 2. Command handling (for bot commands)
 * 3. Context preparation (chat history, user facts, semantic memory)
 * 4. Attachment processing
 * 5. AI response generation with dynamic configuration
 * 6. Response handling and delivery
 * 7. Error handling and notifications
 *
 * The function runs as a background task to avoid blocking the main request-response cycle,
 * providing a responsive experience for users while handling potentially time-consuming
 * AI operations.
 *
 * @module messageProcessor
 */

import { ExaSearchResults } from '@langchain/exa';
import Exa from 'exa-js';

import { sendTelegramError } from '../chat/telegram/index.js';
import { CHAT_TEMPERATURE, CHAT_THINKING_BUDGET, SYSTEM_PROMPT } from '../constants/index.js';
import { extractAndStoreFacts } from '../redis/factExtraction.js';
import { processAttachments } from './attachmentProcessor.js';
import { extract<PERSON><PERSON>backQ<PERSON>y, handleCallbackQuery, hasCallbackQuery } from './callbackHandler.js';
import { prepareChatContext, prepareFactContext } from './chatContext.js';
import { handleCommand } from './commands/index.js';
import { langfuseManager } from './gemini-ai/utils/LangfuseManager.js';
import { callGenerativeAI } from './geminiAI.js';
import { extractMessageData, shouldProcessMessage } from './messageExtractor.js';
import { extractResponseText, handleAIResponse } from './response/aiResponseHandler.js';
import { startTypingIndicator, stopTypingIndicator } from './telegram/typingIndicator.js';
// import { createTaskTools } from './tools/taskTools.js';
import { formatTimestamp } from './utils/formatUtils.js';
// import { getComplexityScore } from './complexity/scorer.js';

/**
 * Processes a Telegram message in the background to generate an AI-powered response.
 * This function orchestrates the complete message processing pipeline, from initial
 * validation through response generation and delivery.
 *
 * @param {Object} env - Environment variables
 * @param {Object} webhookData - Raw Telegram webhook data containing the message
 * @returns {Promise<void>} - Resolves when processing is complete, rejects on critical errors
 *
 * @example
 * await processTelegramMessageInBackground(env, webhookData);
 */
export async function processTelegramMessageInBackground(env, webhookData) {
	// Handle callback queries from inline keyboards (e.g., reminder buttons)
	if (hasCallbackQuery(webhookData)) {
		const callbackQuery = extractCallbackQuery(webhookData);
		await handleCallbackQuery(env, callbackQuery);
		return; // Early return for callback queries
	}

	// Extract relevant message data from the webhook payload
	const messageData = extractMessageData(env, webhookData);
	const { chatId, text, userId, username, messageId, messageDate } = messageData;
	const botUsername = env.TELEGRAM_BOT_USERNAME || '';

	// Validate whether this message should be processed (e.g., not a command to another bot)
	if (!shouldProcessMessage(messageData, botUsername, webhookData)) {
		return;
	}

	// Start showing the typing indicator to provide user feedback
	startTypingIndicator(env, chatId);
	console.log(`Processing message from ${username} (${chatId}) in background: ${text}`);

	try {
		// Handle bot commands (e.g., /start, /help) before processing as regular messages
		const commandHandled = await handleCommand(text, env, messageData, botUsername);
		if (commandHandled) {
			console.log(`Command processed successfully for chat ${chatId}`);
			stopTypingIndicator();
			return; // Exit early since command was handled
		}

		// Prepare user-specific facts that should be included in the AI's context
		// These are personalized details about the user that can inform responses
		const { factsString } = await prepareFactContext(env, userId);

		// Process any attachments (images, documents, etc.) that were sent with the message
		// This extracts text from images, processes documents, and prepares them for AI analysis
		const attachmentParts = await processAttachments(env, messageData);

		// Construct an enhanced system prompt by replacing placeholders with actual values
		// This creates a customized instruction set for the AI model based on the current context
		const enhancedSystemPrompt = SYSTEM_PROMPT.replace('{USER_ID}', userId)
			.replace('{CURRENT_DATE}', formatTimestamp(Date.now() / 1000, env)) // Convert to seconds for Unix timestamp
			.replace('{USER_FACTS}', factsString);

		// TODO: Re-enable complexity scoring
		// let complexityScore = 0;
		// if (text.length > 0) {
		// 	// Get complexity score for the user's message
		// 	complexityScore = await getComplexityScore(env, text, redisHistory);
		// 	console.log(`Complexity score for message: ${complexityScore}`);
		// }

		// Create dynamic tools for the AI to use
		let aiTools = [];

		// Create Exa search tool
		const client = new Exa(env.EXASEARCH_API_KEY);
		const exaTool = new ExaSearchResults({
			client,
			searchArgs: {
				numResults: 10,
				type: 'fast',
				context: {
					maxCharacters: 25000,
				},
				text: {
					maxCharacters: 2000,
				},
				livecrawl: 'always',
			},
		});
		aiTools.push(exaTool);

		// Create all task management tools
		// const taskTools = createTaskTools(env, userId);
		// aiTools = aiTools.concat(taskTools);

		// Configure the AI model with appropriate parameters for this request
		const config = {
			temperature: CHAT_TEMPERATURE, // Controls response randomness
			systemInstruction: enhancedSystemPrompt, // Customized instructions for the AI
			tools: aiTools, // Pass all available tools to the agent
			thinkingBudget: CHAT_THINKING_BUDGET, // Limits on AI processing time/cost
			traceTags: ['chat', `user-${userId}`], // Tags for monitoring and analytics
			isChat: true,
			threadId: chatId,
		};

		// Adjust model selection based on message complexity (currently commented out)
		// if (complexityScore >= 0.5) {
		// 	config.model = 'gemini-2.5-pro'; // Use more powerful model for complex queries
		// }

		// Use pro for select users
		if (env.PRO_USERS.includes(userId)) {
			config.model = 'gemini-2.5-pro';
			config.thinkingBudget = 128;
		}

		// Prepare the message content to send to the AI, including both text and attachments
		const contents = [{ role: 'user', parts: [{ text: text }, ...attachmentParts] }];

		// Call the generative AI model with the prepared context and configuration
		const aiResponse = await callGenerativeAI(env, config, contents);

		// Handle array of responses: use only the last response if array
		let responseText = extractResponseText(aiResponse.text);

		stopTypingIndicator();

		// Extract new facts from current message and store them
		const { previousMessages } = await prepareChatContext(env, chatId, botUsername, messageDate);

		// Handle the AI response by sending it to the user and managing any follow-up actions
		await handleAIResponse(env, chatId, responseText, messageId, text, factsString, previousMessages);

		await extractAndStoreFacts(env, userId, text, previousMessages).catch((error) =>
			console.error('Error during fact extraction:', error),
		);

		// Clean up any resources used by the Langfuse monitoring system
		await langfuseManager.cleanup();
	} catch (backgroundError) {
		// Handle any errors that occur during message processing
		console.error('Error during background Telegram message processing:', backgroundError);

		// Create context for the error notification
		const context = { path: '/hrmny (background)', method: 'POST', chatId: chatId };

		try {
			// Attempt to send an error notification to the admin
			sendTelegramError(env, backgroundError, context);
		} catch (notificationError) {
			// If sending the error notification fails, log this failure
			console.error('Failed to send background processing error notification:', notificationError);
		} finally {
			// Ensure the typing indicator is stopped even if errors occur
			stopTypingIndicator();
		}
	}
}
