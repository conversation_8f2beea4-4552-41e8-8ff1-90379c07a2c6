/**
 * @fileoverview Generates smart reply suggestions based on the conversation context.
 */

import { SUGGESTION_PROMPT, SUGGESTION_SYSTEM_PROMPT, SUGGESTION_TEMPERATURE } from '../../constants/index.js';
import { callGenerativeAI } from '../geminiAI.js';

/**
 * Generates smart reply suggestions.
 * @param {Object} env - The environment variables.
 * @param {string} userMessage - The user's message.
 * @param {string} aiResponse - The AI's response.
 * @param {string} userFacts - The user's extracted facts.
 * @param {string} conversationHistory - The recent conversation history.
 * @returns {Promise<Array<string>>} A list of suggestions.
 */
export async function generateSuggestions(env, userMessage, aiResponse, userFacts, conversationHistory, currentDate, currentTime) {
	try {
		const config = {
			temperature: SUGGESTION_TEMPERATURE,
			systemInstruction: SUGGESTION_SYSTEM_PROMPT,
			// inferenceProvider: 'cerebras',
			model: 'gemini-2.5-flash',
			// thinkingBudget: 1024,
			traceTags: ['suggestion-generation'],
		};

		let prompt = SUGGESTION_PROMPT.replace('{USER_MESSAGE}', userMessage)
			.replace('{AI_RESPONSE}', aiResponse)
			.replace('{USER_FACTS}', userFacts || 'No specific facts available.')
			.replace('{CONVERSATION_HISTORY}', conversationHistory || 'No recent conversation history available.')
			.replace('{CURRENT_DATE}', currentDate)
			.replace('{CURRENT_TIME}', currentTime);

		const contents = [{ role: 'user', parts: [{ text: prompt }] }];
		const suggestionsResponse = await callGenerativeAI(env, config, contents);
		const suggestionsText = _extractResponseText(suggestionsResponse.text);

		if (suggestionsText) {
			return suggestionsText.split('\n').map((s) => s.trim());
		}
	} catch (error) {
		console.error('Error generating suggestions:', error);
	}
	return [];
}

/**
 * Extracts response text from possible response formats
 * @private
 * @param {string|Array|Object} response - The AI response
 * @returns {string} Extracted text
 */
function _extractResponseText(response) {
	if (!response) return '';

	if (Array.isArray(response)) {
		const last = response[response.length - 1];
		return last?.text.trim() || '';
	}

	return typeof response === 'string' ? response.trim() : '';
}
