import { USER_REMINDER_PREFERENCES_KEY, USERS_WITH_TASKS_KEY } from './constants.js';

/**
 * Task Storage module for handling Redis operations
 *
 * This module encapsulates all Redis interactions for task management,
 * providing a clean separation between data persistence and business logic.
 */
export class TaskStorage {
	/**
	 * Create a TaskStorage instance
	 * @param {object} redisClient - Redis client instance for data persistence
	 * @throws {Error} If redisClient is not provided
	 */
	constructor(redisClient) {
		if (!redisClient) {
			throw new Error('Redis client is required for TaskStorage.');
		}
		this.redis = redisClient;
	}

	/**
	 * Get the Redis key for a user's tasks
	 * @param {string} userId - The ID of the user
	 * @returns {string} The Redis key for the user's tasks
	 */
	_getUserTasksKey(userId) {
		return `user:${userId}:tasks`;
	}

	/**
	 * Store a task in Redis
	 * @param {string} userId - The ID of the user
	 * @param {Object} task - The task object to store
	 * @returns {Promise<void>}
	 */
	async storeTask(userId, task) {
		const key = this._getUserTasksKey(userId);
		const tx = this.redis.multi();
		tx.hset(key, { [task.id]: JSON.stringify(task) });
		tx.sadd(USERS_WITH_TASKS_KEY, userId);
		await tx.exec();
	}

	/**
	 * Get all tasks for a user from Redis
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<Object>} Object with task IDs as keys and task data as values
	 */
	async getUserTasks(userId) {
		const key = this._getUserTasksKey(userId);
		return await this.redis.hgetall(key);
	}

	/**
	 * Delete a task from Redis
	 * @param {string} userId - The ID of the user
	 * @param {string} taskId - The ID of the task to delete
	 * @returns {Promise<number>} Number of deleted fields
	 */
	async deleteTask(userId, taskId) {
		const key = this._getUserTasksKey(userId);
		return await this.redis.hdel(key, taskId);
	}

	/**
	 * Get user's reminder preferences from Redis
	 * @param {string} userId - The ID of the user
	 * @returns {Promise<string|null>} JSON string of preferences or null if not found
	 */
	async getUserReminderPreferences(userId) {
		const key = `${USER_REMINDER_PREFERENCES_KEY}:${userId}`;
		return await this.redis.get(key);
	}

	/**
	 * Update user's reminder preferences in Redis
	 * @param {string} userId - The ID of the user
	 * @param {Object} preferences - The preferences to store
	 * @returns {Promise<void>}
	 */
	async updateUserReminderPreferences(userId, preferences) {
		const key = `${USER_REMINDER_PREFERENCES_KEY}:${userId}`;
		await this.redis.set(key, JSON.stringify(preferences));
	}

	/**
	 * Get all user IDs that have tasks
	 * @returns {Promise<string[]>} Array of user IDs
	 */
	async getAllUserIdsWithTasks() {
		return await this.redis.smembers(USERS_WITH_TASKS_KEY);
	}
}
