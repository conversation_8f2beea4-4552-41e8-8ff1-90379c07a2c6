/**
 * Base URL for the Telegram Bot API.
 * @type {string}
 */
const TELEGRAM_API_URL = 'https://api.telegram.org';

/**
 * Endpoint template for getting file information from Telegram.
 * @type {string}
 * @example
 * // Replace {token} with bot token and {fileId} with actual file ID
 * GET_FILE_ENDPOINT.replace('{token}', '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11')
 *   .replace('{fileId}', 'file_id_from_message')
 */
const GET_FILE_ENDPOINT = '/bot{token}/getFile?file_id={fileId}';

/**
 * Template for constructing the direct file URL from Telegram.
 * @type {string}
 * @example
 * // Replace {token} with bot token and {filePath} with path from getFile response
 * TELEGRAM_FILE_URL.replace('{token}', '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11')
 *   .replace('{filePath}', 'photos/file_12.jpg')
 */
const TELEGRAM_FILE_URL = '/file/bot{token}/{filePath}';

/**
 * Checks if the bot is mentioned in a message.
 *
 * This function handles various mention scenarios:
 * - Direct @mentions in message text
 * - Mentions detected in message entities
 * - Replies to the bot's messages
 * - All messages in private chats
 *
 * @param {Object} webhookData - The webhook data from Telegram
 * @param {string} [botUsername] - The bot's username (without @). If not provided,
 * the function treats all messages as mentions and logs a warning.
 * @returns {boolean} True if the bot is mentioned, false otherwise
 *
 * @example
 * // Check if bot is mentioned in a message
 * const isMentioned = checkBotMention(webhookData, 'mybot');
 *
 * @example
 * // In private chat, always returns true
 * const privateMessage = { message: { chat: { type: 'private' } } };
 * checkBotMention(privateMessage, 'mybot'); // returns true
 */
export function checkBotMention(webhookData, botUsername) {
	if (!botUsername) {
		console.warn('TELEGRAM_BOT_USERNAME not configured, treating all messages as mentions');
		return true;
	}
	const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
	if (message?.chat?.type === 'private') {
		return true;
	}
	const botMentionRegex = new RegExp(`@${botUsername}\\b`, 'i');
	if (botMentionRegex.test(message?.text)) {
		return true;
	}
	const entities = message?.entities || [];
	for (const entity of entities) {
		if (entity.type === 'mention') {
			const mentionText = message?.text.substring(entity.offset, entity.offset + entity.length);
			if (mentionText.toLowerCase() === `@${botUsername.toLowerCase()}`) {
				return true;
			}
		}
	}
	return message?.reply_to_message?.from?.username === botUsername;
}

/**
 * Escapes special characters for MarkdownV2 format in Telegram.
 *
 * Telegram's MarkdownV2 has specific rules for formatting. This function:
 * 1. Converts **bold** syntax to *bold* (single asterisks)
 * 2. Escapes standalone asterisks that aren't part of bold formatting
 * 3. Escapes all other special characters that have meaning in MarkdownV2
 *
 * Special characters that are escaped: _ [ ] ( ) ~ ` > # + - = | { } . !
 *
 * @param {string} [text] - The text to escape. If not a string, returns empty string.
 * @returns {string} The escaped text, safe for use with Telegram's MarkdownV2
 *
 * @example
 * escapeMdV2('Hello *world*'); // returns 'Hello \\*world\\*'
 * escapeMdV2('Check **this** out'); // returns 'Check *this* out'
 * escapeMdV2('Price: $10.99'); // returns 'Price: $10\\.99'
 */
export function escapeMdV2(text) {
	if (typeof text !== 'string') {
		return '';
	}
	// First escape all special characters except asterisks
	// This prevents issues with numbers like "908.000" where the period needs to be escaped
	let processedText = text.replace(/[[_\]()~`>#+=|{}.!\\-]/g, '\\$&');
	// Convert **text** to *text*
	processedText = processedText.replace(/\*\*(.*?)\*\*/g, '*$1*');
	// Escape standalone asterisks not part of formatting pairs
	processedText = processedText.replace(/(?<!\*)\*(?!\*)/g, '\\*');
	return processedText;
}

/**
 * Escapes special characters for HTML format in Telegram and converts markdown-style formatting to HTML.
 *
 * Telegram's HTML parsing mode supports a subset of HTML tags. This function:
 * 1. Escapes HTML special characters (&, <, >) to prevent parsing errors
 * 2. Converts **bold** syntax to <b>bold</b> HTML tags
 * 3. Converts *italic* syntax to <i>italic</i> HTML tags
 * 4. Converts `code` syntax to <code>code</code> HTML tags
 * 5. Preserves existing HTML tags that are already properly formatted
 *
 * Supported HTML tags in Telegram: <b>, <strong>, <i>, <em>, <u>, <ins>, <s>, <strike>, <del>,
 * <span class="tg-spoiler">, <tg-spoiler>, <a>, <code>, <pre>
 *
 * @param {string} [text] - The text to escape and format. If not a string, returns empty string.
 * @returns {string} The escaped and HTML-formatted text, safe for use with Telegram's HTML parsing
 *
 * @example
 * escapeHtml('Hello **world**'); // returns 'Hello <b>world</b>'
 * escapeHtml('Check *this* out'); // returns 'Check <i>this</i> out'
 * escapeHtml('Use `code` here'); // returns 'Use <code>code</code> here'
 * escapeHtml('Price: $10.99 & more'); // returns 'Price: $10.99 &amp; more'
 */
export function escapeHtml(text) {
	if (typeof text !== 'string') {
		return '';
	}

	// First escape HTML special characters
	let processedText = text
		.replace(/&/g, '&amp;') // Must be first to avoid double-escaping
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;');

	// Convert markdown-style formatting to HTML tags
	// Handle **bold** syntax (convert to <b>bold</b>)
	processedText = processedText.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>');

	// Handle *italic* syntax (convert to <i>italic</i>)
	// Use negative lookbehind and lookahead to avoid matching asterisks that are part of bold formatting
	// Also ensure we don't match single asterisks in mathematical expressions by requiring word characters
	processedText = processedText.replace(/(?<!\*)\*([a-zA-Z][^*]*?)\*(?!\*)/g, '<i>$1</i>');

	// Handle `code` syntax (convert to <code>code</code>)
	processedText = processedText.replace(/`([^`]+?)`/g, '<code>$1</code>');

	return processedText;
}

/**
 * Fetches JSON from a URL and throws an error if the response is not successful.
 *
 * This utility handles both HTTP-level errors (non-2xx status) and Telegram API errors
 * (responses with ok: false). It's designed for use with Telegram's API endpoints.
 *
 * @param {string} url - The URL to fetch JSON from
 * @param {string} errorContext - Context to include in error messages for easier debugging
 * @returns {Promise<Object>} A promise that resolves to the parsed JSON response
 * @throws {Error} If the HTTP request fails or the API returns an error
 *
 * @example
 * try {
 *   const result = await fetchJsonOrThrow(
 *     'https://api.telegram.org/bot123:ABC/getMe',
 *     'getMe:'
 *   );
 *   console.log(result);
 * } catch (error) {
 *   console.error('API call failed:', error.message);
 * }
 */
export async function fetchJsonOrThrow(url, errorContext) {
	const response = await fetch(url);
	if (!response.ok) {
		throw new Error(`${errorContext} HTTP error! Status: ${response.status}`);
	}
	const json = await response.json();
	if (!json.ok) {
		throw new Error(`${errorContext} API error! Description: ${json.description}`);
	}
	return json;
}

/**
 * Gets the direct URL of a file from Telegram's servers.
 *
 * This function first calls Telegram's getFile API to get the file path,
 * then constructs the direct download URL using the bot token and file path.
 *
 * @param {string} botToken - The bot's authentication token
 * @param {string} fileId - The file identifier from a message
 * @returns {Promise<string>} A promise that resolves to the direct file URL
 * @throws {Error} If there's an error fetching the file information or constructing the URL
 *
 * @example
 * // Get URL for a photo sent to the bot
 * const fileUrl = await getTelegramFile('123:ABC', 'file_id_from_message');
 * console.log(fileUrl); // 'https://api.telegram.org/file/bot123:ABC/photos/file_12.jpg'
 *
 * @example
 * // Handle potential errors
 * try {
 *   const fileUrl = await getTelegramFile(botToken, fileId);
 *   // Use the file URL
 * } catch (error) {
 *   console.error('Failed to get file URL:', error.message);
 * }
 */
export async function getTelegramFile(botToken, fileId) {
	try {
		const getFileUrl = `${TELEGRAM_API_URL}${GET_FILE_ENDPOINT.replace('{token}', botToken).replace('{fileId}', fileId)}`;
		const fileData = await fetchJsonOrThrow(getFileUrl, 'getFile:');
		const filePath = fileData.result.file_path;
		return `${TELEGRAM_API_URL}${TELEGRAM_FILE_URL.replace('{token}', botToken).replace('{filePath}', filePath)}`;
	} catch (err) {
		console.error('Error fetching Telegram file:', err.message);
		throw err;
	}
}

/**
 * Converts an ArrayBuffer to a Base64 string.
 *
 * This is useful for encoding binary data (like images or files) into a text format
 * that can be easily transmitted in JSON or stored in text-based systems.
 *
 * @param {ArrayBuffer} buffer - The ArrayBuffer to convert
 * @returns {string} The Base64-encoded string representation of the buffer
 * @throws {Error} If the browser's btoa function is not available
 *
 * @example
 * // Convert an ArrayBuffer to Base64
 * const buffer = new Uint8Array([72, 101, 108, 108, 111]).buffer; // "Hello"
 * const base64 = arrayBufferToBase64(buffer);
 * console.log(base64); // "SGVsbG8="
 *
 * @example
 * // Common use case: processing file data
 * const response = await fetch('some-binary-file');
 * const arrayBuffer = await response.arrayBuffer();
 * const base64String = arrayBufferToBase64(arrayBuffer);
 */
export function arrayBufferToBase64(buffer) {
	let binary = '';
	const bytes = new Uint8Array(buffer);
	const len = bytes.byteLength;
	for (let i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return btoa(binary);
}
