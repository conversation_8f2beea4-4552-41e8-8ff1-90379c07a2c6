import { USER_ERROR_MESSAGES } from '../../constants/index.js'; // Import the new error messages
import { escapeHtml } from '../telegramUtils.js'; // Import escapeHtml for HTML formatting

/**
 * Sends an error notification via Telegram to both the user and admin.
 * Handles user-friendly error messages based on error type and sends detailed
 * internal error reports to admin chat.
 *
 * @param {Object} env - Environment variables containing Telegram and system configuration
 * @param {Error} error - The error object to report
 * @param {Object} context - Additional context about the error (type, path, method, chatId, etc.)
 * @returns {Promise<void>}
 */
export const sendTelegramError = async (env, error, context = {}) => {
	// Extract required environment variables
	const botToken = env.TELEGRAM_BOT_TOKEN;
	const chatId = env.TELEGRAM_CHAT_ID;

	// Validate required configuration
	if (!botToken || !chatId) {
		console.error('Telegram Bot Token or Chat ID is missing in environment variables.');
		return;
	}

	// Determine appropriate user-friendly error message based on error type
	let userFriendlyMessage = USER_ERROR_MESSAGES.GENERIC_ERROR;
	if (context.type === 'rate_limit') {
		userFriendlyMessage = USER_ERROR_MESSAGES.RATE_LIMIT_ERROR;
	} else if (context.type === 'attachment_processing') {
		userFriendlyMessage = USER_ERROR_MESSAGES.ATTACHMENT_PROCESSING_ERROR;
	}
	// Add more conditions here for other specific error types if needed

	// Create timestamp for the error report using the configured timezone
	const timestamp = new Date().toLocaleString('en-US', {
		timeZone: env.TIMEZONE,
	});

	// Format detailed internal error message for admin notification
	// Uses HTML parsing mode to preserve formatting, especially the <pre> tag for stack trace
	const internalErrorMessage = `🚨 Internal Error Alert 🚨

🕒 Time: ${timestamp} (${env.TIMEZONE})

📍 Path: ${context.path || 'N/A'}
🔗 Method: ${context.method || 'N/A'}
💬 Chat ID: ${context.chatId || 'N/A'}

💥 Message: ${error.message}

Stack:
<pre>${(error.stack || '').slice(0, 1000)}</pre>`; // Limit stack trace length for logs

	// URL for sending messages to users via the HRMNY bot
	const telegramBotUrl = `https://api.telegram.org/bot${env.HRMNY_BOT_TOKEN}/sendMessage`;

	try {
		// First, send user-friendly error message to the user's chat
		const response = await fetch(telegramBotUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				chat_id: context.chatId, // Target the user's chat
				text: escapeHtml(userFriendlyMessage), // Escape special characters for HTML
				parse_mode: 'HTML', // Use HTML for formatted user messages
			}),
		});

		// Handle response from user message attempt
		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send user-friendly Telegram error message: ${response.status} ${response.statusText} - ${
					result.description || 'No description'
				}`,
			);
		} else {
			console.log(`User-friendly error message sent to chat ${context.chatId}`);
		}

		// URL for sending admin notifications using the main bot token
		const telegramUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;

		// Send detailed internal error report to admin chat
		const adminResponse = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				chat_id: chatId, // Send to the configured admin chat
				text: internalErrorMessage,
				parse_mode: 'HTML', // Use HTML parsing to preserve <pre> formatting for stack traces
			}),
		});

		// Handle response from admin message attempt
		if (!adminResponse.ok) {
			const adminResult = await adminResponse.json().catch(() => ({}));
			console.error(
				`Failed to send internal error report to admin chat: ${adminResponse.status} ${adminResponse.statusText} - ${
					adminResult.description || 'No description'
				}`,
			);
		} else {
			console.log(`Internal error report sent to admin chat ${chatId}`);
		}
	} catch (err) {
		// Catch any errors that occur within the error reporting function itself
		console.error('Error in sendTelegramError function itself:', err);
	}
};
