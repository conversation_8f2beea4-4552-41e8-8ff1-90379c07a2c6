// Maximum length for Telegram messages before they need to be split
// Telegram's message limit is approximately 4096 characters, but we use a more conservative
// limit to account for formatting and ensure reliability
// Telegram's message limit is approximately 4096 characters, but we use a more conservative
// limit to account for formatting and ensure reliability
export const MAX_MESSAGE_LENGTH = 1024;

/**
 * Sends a 'typing' chat action to a Telegram chat.
 * This shows the typing indicator in the chat, improving user experience
 * when responses might take a moment to generate.
 *
 * @param {Object} env - Environment variables containing HRMNY_BOT_TOKEN
 * @param {string|number} chatId - The ID of the chat to send the typing action to
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
export const sendTypingAction = async (env, chatId) => {
	// Extract bot token from environment variables
	const botToken = env.HRMNY_BOT_TOKEN;

	// Validate required configuration
	if (!botToken) {
		console.error('Telegram Bot Token is missing in environment variables for typing action.');
		return false;
	}

	// Validate required parameter
	if (!chatId) {
		console.error('Chat ID is required to send a typing action.');
		return false;
	}

	// Construct the Telegram API URL for sendChatAction
	const telegramUrl = `https://api.telegram.org/bot${botToken}/sendChatAction`;

	// Prepare the request payload with chat ID and action type
	const actionData = {
		chat_id: chatId,
		action: 'typing', // Indicates that the bot is typing
	};

	try {
		// Send the request to Telegram API
		const response = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(actionData),
		});

		// Handle HTTP errors
		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send typing action: ${response.status} ${response.statusText} - ${result.description || 'No description'}`,
			);
			return false;
		}

		// Parse the response to check for API-level success
		const responseData = await response.json();

		// Check if Telegram API reported success
		if (responseData.ok) {
			console.log(`Typing action sent to chat ID: ${chatId}`);
			return true;
		} else {
			console.error(`Telegram API reported failure for typing action: ${responseData.description}`);
			return false;
		}
	} catch (error) {
		// Handle network errors or other exceptions
		console.error(`Error sending typing action to chat ID ${chatId}:`, error);
		return false;
	}
};

/**
 * Sends a message to a specific Telegram chat.
 * Handles message formatting and delivery with proper error handling.
 *
 * @param {Object} env - Environment variables containing HRMNY_BOT_TOKEN
 * @param {string|number} chatId - The ID of the chat to send the message to
 * @param {string} text - The message text content (should be HTML-formatted for best results)
 * @param {Object} options - Additional message options
 * @param {string} [options.parseMode] - Text parsing mode ('HTML', 'Markdown', 'MarkdownV2'). Defaults to 'HTML'
 * @param {number} [options.message_id] - If replying, the ID of the message being replied to
 * @returns {Promise<Object|null>} - Telegram API response on success, null on failure
 */
export const sendTelegramMessage = async (env, chatId, text, options = {}, suggestions = []) => {
	// Extract bot token from environment variables
	const botToken = env.HRMNY_BOT_TOKEN;

	// Validate required configuration
	if (!botToken) {
		console.error('Telegram Bot Token is missing in environment variables.');
		return null;
	}

	// Validate required parameter
	if (!chatId) {
		console.error('Chat ID is required to send a Telegram message.');
		return null;
	}

	// Construct the Telegram API URL for sendMessage
	const telegramUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;

	// Prepare the message payload with required and optional parameters
	const messageData = {
		chat_id: chatId,
		text: text,
		parse_mode: options.parseMode || 'HTML', // Default to HTML for consistent formatting
	};

	// Add reply parameters if message_id is provided
	if (options.message_id) {
		messageData.reply_parameters = {
			message_id: options.message_id,
		};
	}

	if (suggestions.length > 0) {
		messageData.reply_markup = {
			keyboard: suggestions.map((suggestion) => [{ text: suggestion }]),
			resize_keyboard: true,
			one_time_keyboard: true,
		};
	} else {
		messageData.reply_markup = {
			remove_keyboard: true,
		};
	}

	try {
		// Send the message request to Telegram API
		const response = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(messageData),
		});

		// Handle HTTP-level errors
		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send Telegram message: ${response.status} ${response.statusText} - ${result.description || 'No description'}`,
			);
			return null;
		}

		// Parse and return the successful response
		const responseData = await response.json();
		console.log(`Telegram message sent to chat ID: ${chatId}`);
		return responseData;
	} catch (error) {
		// Handle network errors or other exceptions
		console.error(`Error sending Telegram message to chat ID ${chatId}:`, error);
		return null;
	}
};

/**
 * Sends a potentially long message to a Telegram chat, automatically splitting it
 * into multiple messages if it exceeds the maximum length.
 * Handles reply threading appropriately by only including reply parameters
 * in the first message.
 *
 * @param {Object} env - Environment variables containing HRMNY_BOT_TOKEN
 * @param {string|number} chatId - The ID of the chat to send the message to
 * @param {string} text - The message text content, which may be long (should be HTML-formatted)
 * @param {Object} options - Additional message options (e.g., reply parameters, parseMode)
 * @returns {Promise<boolean>} - True if all message parts were sent successfully
 */
export const sendLongTelegramMessage = async (env, chatId, text, options = {}, suggestions = []) => {
	// Import the text utility function (using dynamic import to avoid circular dependencies)
	const { splitTextIntoTelegramChunks } = await import('./textUtils.js');

	// If the message is within the length limit, send it directly
	if (text.length <= MAX_MESSAGE_LENGTH) {
		await sendTelegramMessage(env, chatId, text, options, suggestions);
		return true;
	}

	// Split the long message into appropriately sized chunks
	const chunks = splitTextIntoTelegramChunks(text, MAX_MESSAGE_LENGTH);

	// Track whether we're sending the first message (for reply handling)
	let firstMessage = true;

	// Send each chunk as a separate message
	for (let i = 0; i < chunks.length; i++) {
		const chunk = chunks[i];
		// Create a copy of options for this message
		const currentOptions = { ...options };

		// For subsequent messages, remove the reply parameter to avoid
		// replying to the same message multiple times, which could confuse users
		if (!firstMessage) {
			delete currentOptions.message_id;
		}

		// Only include suggestions on the last chunk
		const isLastChunk = i === chunks.length - 1;
		await sendTelegramMessage(env, chatId, chunk, currentOptions, isLastChunk ? suggestions : []);
		firstMessage = false;

		// Add a small delay between messages to prevent rate limiting
		await new Promise((resolve) => setTimeout(resolve, 500));
	}

	return true;
};
