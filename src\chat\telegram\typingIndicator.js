/**
 * @fileoverview Manages the "typing" indicator in Telegram chats.
 *
 * This module provides functions to start and stop the typing indicator,
 * giving users visual feedback that the bot is processing their request.
 * It encapsulates the logic for sending periodic typing actions to keep
 * the indicator active.
 */

import { sendTypingAction } from './index.js';

let typingInterval;

/**
 * Starts the typing indicator for a chat.
 *
 * @param {Object} env - The environment variables.
 * @param {string|number} chatId - The Telegram chat ID.
 */
export const startTypingIndicator = (env, chatId) => {
	sendTypingAction(env, chatId);
	typingInterval = setInterval(() => {
		sendTypingAction(env, chatId);
	}, 5000);
};

/**
 * Stops the typing indicator.
 */
export const stopTypingIndicator = () => {
	if (typingInterval) {
		clearInterval(typingInterval);
		typingInterval = null;
	}
};
