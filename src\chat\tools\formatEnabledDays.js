/**
 * format-enabled-days.js
 *
 * Single responsibility: convert an array of numeric day indices into a human-readable string.
 */

/**
 * Format an array of enabled day indices (0 = Sunday ... 6 = Saturday) into a readable list.
 * @param {number[]|undefined} enabledDays
 * @returns {string}
 */
export function formatEnabledDays(enabledDays = []) {
	if (!Array.isArray(enabledDays) || enabledDays.length === 0) {
		return 'None';
	}

	const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

	const names = enabledDays.filter((d) => typeof d === 'number' && d >= 0 && d <= 6).map((d) => dayNames[d]);

	return names.length > 0 ? names.join(', ') : 'None';
}
