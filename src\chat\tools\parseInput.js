/**
 * Utilities for parsing inputs for task tools.
 * Single responsibility: normalize various input formats into usable values.
 */

/**
 * Parse a task description from various input shapes that LangChain tools may provide.
 * @param {string|object|null|undefined} input
 * @returns {string|null} trimmed description or null if not found
 */
export function parseTaskDescription(input) {
	if (input === null || input === undefined) {
		return null;
	}

	let description;

	if (typeof input === 'string') {
		// Attempt to parse JSON strings like '{"description":"Buy milk"}'
		try {
			const parsed = JSON.parse(input);
			if (parsed && typeof parsed === 'object') {
				description = parsed.description || parsed.task || parsed.text || parsed.content || parsed.message;

				if (!description) {
					const stringValues = Object.values(parsed).filter((val) => typeof val === 'string' && val.trim() !== '');
					if (stringValues.length > 0) {
						description = stringValues[0];
					}
				}
			} else {
				// parsed primitive (number/boolean) or not an object
				description = input;
			}
		} catch {
			// Not JSON — treat the raw string as the description
			description = input;
		}
	} else if (typeof input === 'object') {
		description = input.description || input.task || input.text || input.content || input.message;

		if (!description) {
			const stringValues = Object.values(input).filter((val) => typeof val === 'string' && val.trim() !== '');
			if (stringValues.length > 0) {
				description = stringValues[0];
			}
		}
	} else {
		// Fallback for other primitives (number, boolean, etc.)
		description = String(input);
	}

	if (!description || typeof description !== 'string' || description.trim() === '') {
		return null;
	}

	return description.trim();
}

/**
 * Parse reminder action and settings from input.
 * @param {string|object|null|undefined} input
 * @returns {{action:string,settings:object|undefined}}
 */
export function parseReminderActionAndSettings(input) {
	let action = 'view';
	let settings;

	if (typeof input === 'string') {
		try {
			const parsed = JSON.parse(input);
			action = parsed.action || 'view';
			settings = parsed.settings;
		} catch {
			const lower = input.toLowerCase();
			action = lower.includes('update') || lower.includes('set') ? 'update' : 'view';
		}
	} else if (typeof input === 'object' && input !== null) {
		action = input.action || 'view';
		settings = input.settings;
	} else {
		action = 'view';
	}

	return { action, settings };
}
