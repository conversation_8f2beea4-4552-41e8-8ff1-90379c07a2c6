import { DynamicTool } from '@langchain/core/tools';

import { formatEnabledDays } from './formatEnabledDays.js';
import { parseReminderActionAndSettings } from './parseInput.js';

/**
 * Create a LangChain DynamicTool to view or update reminder settings.
 * Single responsibility: construct and return the reminder_settings tool.
 *
 * @param {TaskManager} taskManager
 * @param {string|number} userId
 * @returns {DynamicTool}
 */
export function reminderSettingsTool(taskManager, userId) {
	return new DynamicTool({
		name: 'reminder_settings',
		description:
			'Use this tool to view or update user reminder preferences including timezone, preferred times, quiet hours, and reminder frequency settings.',
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot manage reminder settings.';
				}

				const { action, settings } = parseReminderActionAndSettings(input);

				if (action === 'view') {
					const preferences = await taskManager.getUserReminderPreferences(userId);

					let response = '⚙️ **Your Reminder Settings:**\n\n';
					response += `🌍 **Timezone:** ${preferences?.timezone}\n`;
					response += `⏰ **Preferred Time:** ${preferences?.preferredReminderTime}\n`;
					response += `📅 **Enabled Days:** ${formatEnabledDays(preferences?.enabledDays)}\n`;
					response += `🔢 **Max Reminders/Day:** ${preferences?.maxRemindersPerDay}\n`;
					response += `🌙 **Quiet Hours:** ${preferences?.quietHours?.start} - ${preferences?.quietHours?.end}\n\n`;

					response += '**Reminder Types:**\n';
					Object.entries(preferences?.reminderTypes || {}).forEach(([type, enabled]) => {
						response += `• ${type}: ${enabled ? '✅' : '❌'}\n`;
					});

					response +=
						'\nTo update settings, tell me what you\'d like to change (e.g., "Set my timezone to America/New_York" or "Change quiet hours to 10 PM - 7 AM").';

					return response;
				} else if (action === 'update' && settings) {
					await taskManager.updateUserReminderPreferences(userId, settings);
					return '✅ Reminder settings updated successfully! Use the reminder_settings tool again to view your new settings.';
				} else {
					return 'Please specify what reminder settings you\'d like to view or update. For example: "Show my reminder settings" or provide specific settings to update.';
				}
			} catch (error) {
				console.error('Error in reminder_settings tool:', error);
				return 'Error: Failed to manage reminder settings due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				action: {
					type: 'string',
					enum: ['view', 'update'],
					description: 'Whether to view current settings or update them.',
				},
				settings: {
					type: 'object',
					description: 'New reminder settings to apply (only needed for update action).',
				},
			},
		},
	});
}
