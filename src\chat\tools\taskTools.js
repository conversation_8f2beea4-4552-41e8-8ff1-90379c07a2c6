/**
 * taskTools.js
 *
 * Centralized factory that wires modular tool creators into a single export.
 * Single responsibility: build and return LangChain DynamicTool instances for a user.
 */

import { getRedisClient } from '../../redis/redisClient.js';
import { TaskManager } from '../tasks/taskManager.js';
import { completeTaskTool } from './completeTaskTool.js';
import { createTaskTool } from './createTaskTool.js';
import { deleteTaskTool } from './deleteTaskTool.js';
import { listTasksTool } from './listTasksTool.js';
import { reminderSettingsTool } from './reminderSettingsTool.js';

/**
 * Creates all task management tools for a specific user.
 * This function centralizes tool creation and ensures a single TaskManager instance is used.
 *
 * @param {object} env - The environment variables.
 * @param {string|number} userId - The ID of the user for whom the tools will be created.
 * @returns {Array<DynamicTool>} An array of LangChain tool instances.
 */
export function createTaskTools(env, userId) {
	const redisClient = getRedisClient(env);
	const taskManager = new TaskManager(redisClient);

	return [
		createTaskTool(taskManager, userId),
		listTasksTool(taskManager, userId),
		completeTaskTool(taskManager, userId),
		deleteTaskTool(taskManager, userId),
		reminderSettingsTool(taskManager, userId),
	];
}
