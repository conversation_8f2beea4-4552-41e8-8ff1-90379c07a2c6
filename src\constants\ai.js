/**
 * @fileoverview This file contains constants related to AI model behavior and parameters.
 */

// --- Cha<PERSON>vior ---

/**
 * The temperature for the main chat model. This value controls the randomness
 * of the AI's responses. Higher values (e.g., 1.0) make the output more random,
 * while lower values (e.g., 0.2) make it more deterministic.
 * @type {number}
 */
export const CHAT_TEMPERATURE = 0.85;

/**
 * The thinking budget for the chat model, in tokens. This is not a standard
 * parameter and may be used for custom logic to limit prompt size or complexity.
 * @type {number}
 */
export const CHAT_THINKING_BUDGET = 8192;

// --- Response Refinement ---

/**
 * The temperature for the response refinement model. A lower value is used
 * for more deterministic and focused cleanup of the AI's response.
 * @type {number}
 */
export const REFINE_RESPONSE_TEMPERATURE = 0.2;

// --- Suggestion Generation ---

/**
 * The temperature for the suggestion generation model. A higher value
 * encourages more creative and varied suggestions.
 * @type {number}
 */
export const SUGGESTION_TEMPERATURE = 0.7;

// --- Response Summary ---

/**
 * The temperature for the response summary model. A low value ensures that the
 * summary is deterministic and accurately reflects the AI's response.
 * @type {number}
 */
export const SUMMARY_TEMPERATURE = 0.1;
