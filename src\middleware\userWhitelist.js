/**
 * @fileoverview User whitelist middleware for the Harmony bot.
 *
 * This middleware checks if a user is authorized to use the bot by validating
 * their user ID against a whitelist stored in environment variables.
 * Unauthorized users receive a polite message and their attempts are logged.
 */

import { extractMessageData } from '../chat/messageExtractor.js';
import { sendTelegramMessage } from '../chat/telegram/index.js';

// Simple module-level cache for whitelist to avoid rebuilding on every request
let CACHED_WHITELIST = null;

function getCachedWhitelist(env) {
	if (!CACHED_WHITELIST) {
		CACHED_WHITELIST = parseWhitelist(env);
	}
	return CACHED_WHITELIST;
}

/**
 * Parses the whitelist from environment variables.
 * Supports both comma-separated string and individual numbered variables.
 *
 * @param {Object} env - Environment variables
 * @returns {Set<string>} Set of whitelisted user IDs as strings
 */
function parseWhitelist(env) {
	const whitelist = new Set();

	// Method 1: Comma-separated list in WHITELISTED_USERS
	if (env.WHITELISTED_USERS) {
		const userIds = env.WHITELISTED_USERS.split(',')
			.map((id) => id.trim())
			.filter((id) => id.length > 0);
		userIds.forEach((id) => whitelist.add(id));
	}

	// Method 2: Individual numbered variables (WHITELISTED_USER_1, WHITELISTED_USER_2, etc.)
	let i = 1;
	while (env[`WHITELISTED_USER_${i}`]) {
		const userId = env[`WHITELISTED_USER_${i}`].trim();
		if (userId.length > 0) {
			whitelist.add(userId);
		}
		i++;
	}

	return whitelist;
}

/**
 * Checks if a user ID is in the whitelist.
 *
 * @param {string|number} userId - The user ID to check
 * @param {Set<string>} whitelist - Set of whitelisted user IDs
 * @returns {boolean} True if user is whitelisted, false otherwise
 */
function isUserWhitelisted(userId, whitelist) {
	if (!userId) return false;
	return whitelist.has(String(userId));
}

/**
 * Sends a polite unauthorized access message to the user.
 *
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID to send the message to
 * @param {string} username - The username of the unauthorized user
 * @returns {Promise<void>}
 */
async function sendUnauthorizedMessage(env, chatId, username) {
	const message = `Hi ${username || 'there'}! 👋

I'm currently in a limited access mode and only available to authorized users. 

If you believe you should have access, please contact the administrator.

Thanks for understanding! 😊`;

	await sendTelegramMessage(env, chatId, message, { parseMode: 'HTML' });
}

/**
 * Logs unauthorized access attempts for monitoring.
 *
 * @param {string|number} userId - The user ID that attempted access
 * @param {string} username - The username that attempted access
 * @param {string|number} chatId - The chat ID where the attempt occurred
 * @param {string} text - The message text that was sent
 */
function logUnauthorizedAccess(userId, username, chatId, text) {
	console.warn(
		`[UNAUTHORIZED ACCESS] User ${username} (ID: ${userId}) in chat ${chatId} attempted to use bot. Message: "${text?.substring(0, 100)}${
			text?.length > 100 ? '...' : ''
		}"`,
	);
}

/**
 * Middleware to check if users are whitelisted to use the harmony bot.
 *
 * This middleware:
 * 1. Extracts user information from Telegram webhook data
 * 2. Checks if the user is in the whitelist
 * 3. If not whitelisted, sends a polite message and blocks further processing
 * 4. If whitelisted, allows the request to continue
 * 5. Logs all unauthorized access attempts
 *
 * @param {Object} c - Hono context object
 * @param {Function} next - Next middleware function
 * @returns {Promise<Response|void>} Response if unauthorized, void if authorized
 */
export const userWhitelistMiddleware = async (c, next) => {
	// Only apply whitelist to the harmony bot endpoint
	if (!c.req.path.includes('/hrmny')) {
		await next();
		return;
	}

	// Skip whitelist check if disabled
	if (c.env.DISABLE_USER_WHITELIST === 'true') {
		await next();
		return;
	}

	try {
		// Parse webhook data once and attach to context for reuse downstream
		let webhookData = c.get('webhookData');
		if (!webhookData) {
			webhookData = await c.req.json();
			c.set('webhookData', webhookData);
		}

		// Skip if no message or callback query (invalid webhook data)
		if (!webhookData.message && !webhookData.callback_query) {
			await next();
			return;
		}

		// Extract user data from the webhook
		const messageData = extractMessageData(c.env, webhookData);
		const { userId, username, chatId, text } = messageData;

		// Skip if no user ID (shouldn't happen with valid Telegram data)
		if (!userId) {
			await next();
			return;
		}

		// Get the whitelist from cache
		const whitelist = getCachedWhitelist(c.env);

		// If no whitelist is configured, allow all users (fail open)
		if (whitelist.size === 0) {
			console.warn('[USER WHITELIST] No whitelist configured. Allowing all users.');
			await next();
			return;
		}

		// Check if user is whitelisted
		if (isUserWhitelisted(userId, whitelist)) {
			// User is authorized, continue processing
			await next();
			return;
		}

		// User is not whitelisted - block access
		logUnauthorizedAccess(userId, username, chatId, text);

		// Send polite unauthorized message to user
		c.executionCtx.waitUntil(sendUnauthorizedMessage(c.env, chatId, username));

		// Return success response to Telegram (to acknowledge webhook)
		// but don't process the message further
		return c.json({
			success: true,
			message: 'Webhook received - user not authorized',
		});
	} catch (error) {
		// If there's an error in whitelist processing, log it but don't block
		// This ensures the bot continues working even if whitelist has issues
		console.error('[USER WHITELIST] Error processing whitelist:', error);
		await next();
		return;
	}
};
