import { FACT_EXTRACTION_ERRORS } from '../constants/errors.js';
import { FactExtractor } from './services/FactExtractor.js';
import { FactStorage } from './services/FactStorage.js';

/**
 * Extracts facts from user text and stores them in the database.
 *
 * This function orchestrates the complete fact extraction and storage workflow:
 * 1. Validates input parameters
 * 2. Extracts facts from the provided text using AI analysis
 * 3. Stores the extracted facts in persistent storage
 * 4. Tracks metrics throughout the process
 * 5. Handles errors gracefully with appropriate responses
 *
 * The function follows a service-oriented architecture, delegating specific
 * responsibilities to specialized service classes:
 * - FactExtractor: Handles the AI-powered fact extraction logic
 * - FactStorage: Manages persistent storage of facts
 *
 * @param {Object} env - Environment variables containing configuration and secrets
 * @param {string} userId - The ID of the user whose facts are being processed
 * @param {string} text - The text content to extract facts from
 * @param {Array<Object>} messageHistory - Optional message history for context
 * @returns {Object} An object containing the operation result with success status,
 *                   metrics, and either stored facts count or error information
 *
 * @example
 * const result = await extractAndStoreFacts(env, 'user123', 'My name is <PERSON> and I live in New York');
 * if (result.success) {
 *   console.log(`Successfully stored ${result.storedCount} facts`);
 * } else {
 *   console.error('Failed to extract and store facts:', result.error);
 * }
 */
export const extractAndStoreFacts = async (env, userId, text, previousMessages) => {
	try {
		// Validate required input parameters
		if (!text) {
			// Return a structured error response for empty input
			// This prevents unnecessary processing and provides clear feedback
			return {
				success: false,
				error: 'Input text is empty',
				errorType: FACT_EXTRACTION_ERRORS.PROCESSING_ERROR,
			};
		}

		const messageHistory = previousMessages.slice(-5);
		// Extract facts from the text using AI analysis
		// The FactExtractor uses the environment configuration and message history
		// to provide context-aware fact extraction
		const factExtractor = new FactExtractor(env);
		const extractedFacts = await factExtractor.extractFacts(userId, text, messageHistory);

		// Store the extracted facts in persistent storage
		// The FactStorage service handles the database operations
		const factStorage = new FactStorage(env);
		const storedCount = await factStorage.storeFacts(userId, extractedFacts);

		// Return a success result with stored count
		return {
			success: true,
			storedCount,
		};
	} catch (error) {
		// Handle any unexpected errors that occur during processing
		console.error('[extractAndStoreFacts] Error:', {
			errorType: FACT_EXTRACTION_ERRORS.PROCESSING_ERROR,
			message: error.message,
		});

		return {
			success: false,
			error: error.message,
			errorType: FACT_EXTRACTION_ERRORS.PROCESSING_ERROR,
		};
	}
};

/**
 * Retrieves all facts associated with a specific user.
 *
 * This function provides a simple interface to fetch all stored facts for a user
 * from the persistent storage. It delegates the actual data retrieval to the
 * FactStorage service, maintaining separation of concerns.
 *
 * @param {Object} env - Environment variables containing configuration and secrets
 * @param {string} userId - The ID of the user whose facts should be retrieved
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of fact objects
 *
 * @example
 * const facts = await getUserFacts(env, 'user123');
 * console.log(`Found ${facts.length} facts for user`);
 * facts.forEach(fact => console.log(fact.text));
 */
export const getUserFacts = async (env, userId) => {
	// Create a FactStorage instance with the environment configuration
	const factStorage = new FactStorage(env);

	// Delegate to the FactStorage service to retrieve user facts
	// This maintains separation of concerns and allows for consistent
	// error handling and data access patterns across the application
	return factStorage.getUserFacts(userId);
};
