/**
 * FactExtractor - A service class for extracting factual information from user messages
 * using generative AI. Extracts discrete facts from conversations to build user profiles.
 *
 * @class
 * @param {Object} env - Environment variables including AI configuration and Redis connection
 */
import { callGenerativeAI } from '../../chat/geminiAI.js';
import { formatRedisHistory } from '../../chat/history/historyManager.js';

export class FactExtractor {
	/**
	 * Creates an instance of FactExtractor
	 *
	 * @constructor
	 * @param {Object} env - Environment configuration containing:
	 *   - AI service credentials
	 *   - Redis connection details
	 *   - Application settings (including TIMEZONE)
	 */
	constructor(env) {
		this.env = env;
	}

	/**
	 * Extracts factual information from a user message within conversation context
	 *
	 * This method:
	 * 1. Loads prompt templates from constants
	 * 2. Formats current date using user's timezone
	 * 3. Formats conversation history for context
	 * 4. Constructs a complete AI prompt with all necessary variables
	 * 5. Calls the AI service to analyze the message
	 * 6. Processes and parses the AI response into structured facts
	 *
	 * @async
	 * @param {string} userId - Unique identifier for the user
	 * @param {string} text - The user's current message text
	 * @param {Array} messageHistory - Array of previous messages in the conversation
	 * @returns {Array<string>} Array of extracted facts, or empty array if none found
	 */
	async extractFacts(userId, text, messageHistory) {
		// Load prompt templates for fact extraction
		const { EXTRACTION_PROMPT, EXTRACTION_SYSTEM_PROMPT } = await import('../../constants/index.js');

		// Format current date/time using the configured timezone
		const currentDate = new Date().toLocaleString('en-US', { timeZone: this.env.TIMEZONE });

		// Format conversation history to provide context for the AI
		const context = formatRedisHistory(messageHistory, this.env, true);

		// Construct the complete prompt by replacing placeholders with actual values
		const prompt = EXTRACTION_PROMPT.replace('{CURRENT_DATE}', currentDate)
			.replace('{USER_ID}', userId)
			.replace('{USER_MESSAGE}', text)
			.replace('{HISTORY}', context);

		// Call AI service with the constructed prompt and system instructions
		const response = await this._callAI(prompt, EXTRACTION_SYSTEM_PROMPT);

		// Extract clean text response from AI output (handling various response formats)
		const responseText = this._extractResponseText(response.text);

		// Parse the AI response into an array of discrete facts
		return this._parseFacts(responseText, userId);
	}

	/**
	 * Calls the generative AI service with the provided prompt and configuration
	 *
	 * Uses a low temperature setting (0.1) for fact extraction to ensure:
	 * - High factual accuracy
	 * - Minimal creative interpretation
	 * - Consistent, deterministic responses
	 *
	 * @async
	 * @private
	 * @param {string} prompt - The user message prompt for the AI
	 * @param {string} systemPrompt - System-level instructions guiding AI behavior
	 * @returns {Object} The AI service response containing generated content
	 */
	async _callAI(prompt, systemPrompt) {
		// Low temperature setting ensures factual accuracy and consistency
		const temperature = 0.1;

		// Configuration for the AI request
		const config = {
			temperature: temperature, // Low temp for factual responses
			systemInstruction: systemPrompt, // System-level guidance
			// inferenceProvider: 'groq', // AI provider
			model: 'gemini-2.5-flash', // Model for fact extraction
			traceTags: ['fact-extract'], // For monitoring and debugging
			thinkingBudget: 8192,
		};

		// Format the message content for the AI API
		const contents = [{ role: 'user', parts: [{ text: prompt }] }];

		// Execute the AI call with environment configuration
		return callGenerativeAI(this.env, config, contents);
	}

	/**
	 * Extracts clean text content from various possible AI response formats
	 *
	 * Handles multiple response structures:
	 * - Direct string responses
	 * - Array responses (multiple content parts)
	 * - Object responses with text properties
	 * - Null/undefined/empty responses
	 *
	 * @private
	 * @param {*} response - Raw response from AI service in various possible formats
	 * @returns {string} Cleaned text content, or empty string if no valid text found
	 */
	_extractResponseText(response) {
		if (!response) return '';

		// Handle array responses (multiple content parts) - use the last part
		if (Array.isArray(response)) {
			const last = response[response.length - 1];
			return last?.text.trim() || '';
		}

		// Handle direct string responses
		return typeof response === 'string' ? response.trim() : '';
	}

	/**
	 * Parses the AI response text into an array of clean, structured facts
	 *
	 * Processing steps:
	 * 1. Validates response existence
	 * 2. Checks for explicit "no facts" indicator
	 * 3. Splits response by newlines into individual fact candidates
	 * 4. Cleans each fact by removing list markers (bullets, numbers)
	 * 5. Filters out very short or empty facts (less than 6 characters)
	 * 6. Logs extraction results for monitoring
	 *
	 * @private
	 * @param {string} response - Raw text response from AI service
	 * @param {string} userId - Identifier for logging purposes
	 * @returns {Array<string>} Array of cleaned facts, or empty array if none valid
	 */
	_parseFacts(response, userId) {
		// Handle null/undefined/empty responses
		if (!response) {
			console.warn(`[FactExtractor] No response from AI for user ${userId}`);
			return [];
		}

		// Check for explicit indicator that no facts were found
		if (response.toUpperCase() === 'NO_FACTS_FOUND') {
			console.log(`[FactExtractor] No facts found for ${userId}`);
			return [];
		}

		// Process the response text into discrete facts
		const facts = response
			.split('\n') // Split by newlines
			.map(
				(fact) =>
					fact
						.replace(/^[-•*]\s*/, '') // Remove bullet points
						.replace(/^\d+\.\s*/, ''), // Remove numbered lists
			)
			.filter((fact) => fact.length > 5); // Filter out very short facts

		// Log the extraction result for monitoring
		console.log(`[FactExtractor] Extracted ${facts.length} facts for ${userId}`);

		return facts;
	}
}
