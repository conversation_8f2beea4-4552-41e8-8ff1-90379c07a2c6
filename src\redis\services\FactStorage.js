/**
 * FactStorage - A service class for managing persistent storage of user facts in Redis
 * with intelligent refinement capabilities. Handles deduplication, merging, and
 * intelligent refinement of facts using AI when necessary.
 *
 * @class
 * @param {Object} env - Environment variables including Redis configuration and AI settings
 */
import { callFastGenerativeAI } from '../../chat/geminiAI.js';
import { FACT_REFINEMENT_PROMPT } from '../../constants/index.js';
import { getRedisClient } from '../redisClient.js';

export class FactStorage {
	/**
	 * Creates an instance of FactStorage
	 *
	 * @constructor
	 * @param {Object} env - Environment configuration containing:
	 *   - Redis connection details
	 *   - AI service credentials
	 *   - Application settings
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	/**
	 * Stores user facts in Redis with intelligent refinement
	 *
	 * This method:
	 * 1. Validates that facts exist to store
	 * 2. Retrieves existing facts for the user from Redis
	 * 3. Refines (merges, deduplicates, improves) existing and new facts using AI
	 * 4. Atomically stores the refined facts in Redis
	 * 5. Logs the operation result
	 *
	 * @async
	 * @param {string} userId - Unique identifier for the user
	 * @param {Array<string>} facts - Array of new facts to store
	 * @returns {number} The number of facts successfully stored (after refinement)
	 */
	async storeFacts(userId, facts) {
		// Skip storage if no facts provided
		if (!facts.length) return 0;

		// Create Redis key for user's facts set
		const factsKey = `user:${userId}:facts`;

		// Retrieve existing facts from Redis
		const existingFacts = await this.redis.smembers(factsKey);

		// Refine facts by merging existing and new facts intelligently
		const refinedFacts = await this._refineFacts(existingFacts, facts);

		// Atomically store refined facts in Redis (prevents race conditions)
		await this._atomicStore(factsKey, refinedFacts);

		// Log successful storage
		console.log(`[FactStorage] Stored ${refinedFacts.length} facts for user ${userId}`);

		return refinedFacts.length;
	}

	/**
	 * Retrieves all facts for a specific user from Redis storage
	 *
	 * Uses Redis SMEMBERS command to get all members of the user's facts set.
	 * Implements error handling to ensure graceful degradation if Redis access fails.
	 *
	 * @async
	 * @param {string} userId - Unique identifier for the user
	 * @returns {Array<string>} Array of facts for the user, or empty array if none found or on error
	 */
	async getUserFacts(userId) {
		// Create Redis key for user's facts set
		const factsKey = `user:${userId}:facts`;

		try {
			// Retrieve all facts from Redis set
			return await this.redis.smembers(factsKey);
		} catch (error) {
			// Log error but return empty array to prevent cascading failures
			console.error(`[FactStorage] Error getting facts for user ${userId}:`, error);
			return [];
		}
	}

	/**
	 * Refines facts by intelligently merging existing and new facts using AI
	 *
	 * Processing logic:
	 * 1. Creates a basic deduplicated set of all facts (fallback)
	 * 2. If both existing and new facts exist, uses AI to intelligently merge:
	 *    - Resolves contradictions
	 *    - Removes redundancies
	 *    - Improves fact quality and phrasing
	 *    - Maintains factual accuracy
	 * 3. Returns AI-refined facts, or basic deduplicated set on failure
	 *
	 * Uses a low temperature setting (0.1) to ensure factual accuracy and minimize creativity.
	 *
	 * @async
	 * @private
	 * @param {Array<string>} existingFacts - Current facts stored for the user
	 * @param {Array<string>} newFacts - New facts to be added
	 * @returns {Array<string>} Refined facts after merging and deduplication
	 */
	async _refineFacts(existingFacts, newFacts) {
		// Create a basic deduplicated set of all facts (used as fallback)
		const allFacts = [...new Set([...existingFacts, ...newFacts])];

		// If either set is empty, return the combined deduplicated set
		if (existingFacts.length < 1 || newFacts.length < 1) return allFacts;

		// Construct prompt for AI to merge and refine facts
		const userPrompt = `<INSTRUCTIONS>Merge and deduplicate these facts</INSTRUCTIONS>\n<EXISTING_FACTS>\n${existingFacts.join(
			'\n',
		)}</EXISTING_FACTS>\n\n<NEW_FACTS>\n${newFacts.join('\n')}</NEW_FACTS>`;

		// Configuration for AI refinement
		const temperature = 0.1; // Low temperature for factual accuracy
		const systemPrompt = FACT_REFINEMENT_PROMPT;

		// Request configuration
		const config = {
			temperature: temperature, // Low temp for factual responses
			systemInstruction: systemPrompt, // System-level guidance
			inferenceProvider: 'cerebras', // AI provider
			model: 'gpt-oss-120b', // Model optimized for fact refinement
			traceTags: ['fact-refine'], // For monitoring and debugging
		};

		// Format message content for AI API
		const contents = [{ role: 'user', parts: [{ text: userPrompt }] }];

		// Call AI service to refine facts
		const response = await callFastGenerativeAI(this.env, config, contents);

		// Return basic deduplicated set if AI call fails
		if (!response) return allFacts;

		// Extract clean text from AI response
		const responseText = this._extractResponseText(response.text);

		// Process AI response into clean facts
		return responseText
			.split('\n') // Split by newlines
			.map((fact) => fact.replace(/^[-•*]\s*/, '')) // Remove bullet points
			.filter((fact) => fact.length > 5); // Filter out very short facts
	}

	/**
	 * Extracts clean text content from various possible AI response formats
	 *
	 * Handles multiple response structures:
	 * - Direct string responses
	 * - Array responses (multiple content parts)
	 * - Object responses with text properties
	 * - Null/undefined/empty responses
	 *
	 * This method is shared with FactExtractor for consistency in handling AI responses.
	 *
	 * @private
	 * @param {*} response - Raw response from AI service in various possible formats
	 * @returns {string} Cleaned text content, or empty string if no valid text found
	 */
	_extractResponseText(response) {
		if (!response) return '';

		// Handle array responses (multiple content parts) - use the last part
		if (Array.isArray(response)) {
			const last = response[response.length - 1];
			return last?.text.trim() || '';
		}

		// Handle direct string responses
		return typeof response === 'string' ? response.trim() : '';
	}

	/**
	 * Atomically stores facts in Redis using a pipeline transaction
	 *
	 * This method ensures data consistency by:
	 * 1. Creating a Redis pipeline for atomic operations
	 * 2. Deleting the existing facts set (ensures clean state)
	 * 3. Adding new facts to the set (if any)
	 * 4. Executing all operations as a single atomic transaction
	 *
	 * Using atomic storage prevents race conditions when multiple processes
	 * might be updating the same user's facts simultaneously.
	 *
	 * @async
	 * @private
	 * @param {string} factsKey - Redis key for the user's facts set
	 * @param {Array<string>} facts - Array of facts to store
	 */
	async _atomicStore(factsKey, facts) {
		// Create Redis pipeline for atomic operations
		const pipeline = this.redis.pipeline();

		// Delete existing facts set (ensures clean state)
		pipeline.del(factsKey);

		// Add new facts to the set (if any)
		if (facts.length > 0) {
			pipeline.sadd(factsKey, ...facts);
		}

		// Execute all operations as a single atomic transaction
		await pipeline.exec();
	}
}
