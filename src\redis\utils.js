import { ChatHistoryManager } from './services/ChatHistoryManager.js';
import { UserFactsManager } from './services/UserFactsManager.js';

/**
 * Deletes chat history for a specific chat and bot.
 * @param {object} env - Cloudflare Worker environment variables
 * @param {string} chatId - The ID of the chat whose history should be deleted
 * @param {string} botUsername - The username of the bot
 * @returns {Promise} Promise that resolves when the chat history is deleted
 */
export const deleteChatHistory = async (env, chatId, botUsername) => {
	const manager = new ChatHistoryManager(env);
	return manager.deleteChatHistory(chatId, botUsername);
};

/**
 * Deletes user facts for a specific user.
 * @param {object} env - Cloudflare Worker environment variables
 * @param {string} userId - The ID of the user whose facts should be deleted
 * @returns {Promise} Promise that resolves when the user facts are deleted
 */
export const deleteUserFacts = async (env, userId) => {
	const manager = new UserFactsManager(env);
	return manager.deleteUserFacts(userId);
};
