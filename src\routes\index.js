import { Hono } from 'hono';

import healthRoutes from './health.routes.js';
import simpleRoutes from './simple.routes.js';
import telegramRoutes from './telegram.routes.js';
import thoughtsRoutes from './thoughts.routes.js';

const routes = new Hono();

// Register all route modules
// Health check endpoints
routes.route('/', healthRoutes);
// Simple utility endpoints
routes.route('/', simpleRoutes);
// Telegram webhook endpoints
routes.route('/', telegramRoutes);
// AI thoughts/monologue generation endpoints
routes.route('/', thoughtsRoutes);

export default routes;
