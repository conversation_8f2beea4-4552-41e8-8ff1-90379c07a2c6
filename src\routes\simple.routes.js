import { Hono } from 'hono';

const simpleRoutes = new Hono();

/**
 * Root Endpoint
 * Redirects to the project's GitHub repository
 */
simpleRoutes.get('/', (c) => {
	// Redirect to the project's GitHub repository or a documentation page
	return c.redirect('https://github.com/hudzax/amai-lyrics', 302); // Use 302 for temporary redirect
});

/**
 * Ping Endpoint
 * Simple health check endpoint that returns "pong"
 */
simpleRoutes.get('/ping', (c) => c.text('pong'));

/**
 * CORS Preflight Handler
 * Handles OPTIONS requests for CORS preflight checks
 */
simpleRoutes.options('*', (c) => {
	// CORS headers should be handled by the global cors middleware
	// This handler just needs to return OK for the preflight request
	return c.text('', 204); // 204 No Content is standard for OPTIONS preflight success
});

export default simpleRoutes;
