/**
 * @fileoverview Circuit breaker pattern implementation to prevent cascading failures
 */

/**
 * Circuit breaker states
 */
const STATES = {
	CLOSED: 'CLOSED', // Normal operation
	OPEN: 'OPEN', // Circuit is open, failing fast
	HALF_OPEN: 'HALF_OPEN', // Testing if service is back
};

/**
 * Circuit breaker implementation
 */
export class CircuitBreaker {
	constructor(options = {}) {
		this.failureThreshold = options.failureThreshold || 5;
		this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minute
		this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
		this.expectedErrorRate = options.expectedErrorRate || 0.5; // 50%

		this.state = STATES.CLOSED;
		this.failureCount = 0;
		this.successCount = 0;
		this.lastFailureTime = null;
		this.nextAttemptTime = null;

		// Statistics for monitoring
		this.stats = {
			totalRequests: 0,
			totalFailures: 0,
			totalSuccesses: 0,
			stateChanges: 0,
		};
	}

	/**
	 * Execute a function with circuit breaker protection
	 * @param {Function} fn - Function to execute
	 * @returns {Promise} Result of the function or circuit breaker error
	 */
	async execute(fn) {
		this.stats.totalRequests++;

		// Check if circuit should transition from OPEN to HALF_OPEN
		if (this.state === STATES.OPEN) {
			if (Date.now() >= this.nextAttemptTime) {
				this._setState(STATES.HALF_OPEN);
				console.log('[CircuitBreaker] Transitioning to HALF_OPEN state for testing');
			} else {
				// Circuit is still open, fail fast
				const error = new Error('Circuit breaker is OPEN - failing fast');
				error.circuitBreakerOpen = true;
				throw error;
			}
		}

		try {
			const result = await fn();
			this._onSuccess();
			return result;
		} catch (error) {
			this._onFailure();
			throw error;
		}
	}

	/**
	 * Handle successful execution
	 * @private
	 */
	_onSuccess() {
		this.successCount++;
		this.stats.totalSuccesses++;

		if (this.state === STATES.HALF_OPEN) {
			// Success in HALF_OPEN state, close the circuit
			this._setState(STATES.CLOSED);
			this.failureCount = 0;
			console.log('[CircuitBreaker] Service recovered, closing circuit');
		} else if (this.state === STATES.CLOSED) {
			// Reset failure count on success in CLOSED state
			this.failureCount = 0;
		}
	}

	/**
	 * Handle failed execution
	 * @private
	 */
	_onFailure() {
		this.failureCount++;
		this.stats.totalFailures++;
		this.lastFailureTime = Date.now();

		if (this.state === STATES.CLOSED || this.state === STATES.HALF_OPEN) {
			if (this.failureCount >= this.failureThreshold) {
				// Open the circuit
				this._setState(STATES.OPEN);
				this.nextAttemptTime = Date.now() + this.recoveryTimeout;
				console.log(
					`[CircuitBreaker] Opening circuit due to ${this.failureCount} failures. Next attempt at: ${new Date(this.nextAttemptTime).toISOString()}`,
				);
			}
		}
	}

	/**
	 * Set circuit breaker state
	 * @private
	 */
	_setState(newState) {
		if (this.state !== newState) {
			console.log(`[CircuitBreaker] State change: ${this.state} -> ${newState}`);
			this.state = newState;
			this.stats.stateChanges++;
		}
	}

	/**
	 * Get current circuit breaker status
	 */
	getStatus() {
		return {
			state: this.state,
			failureCount: this.failureCount,
			successCount: this.successCount,
			lastFailureTime: this.lastFailureTime,
			nextAttemptTime: this.nextAttemptTime,
			isOpen: this.state === STATES.OPEN,
			canExecute: this.state !== STATES.OPEN || Date.now() >= this.nextAttemptTime,
			stats: { ...this.stats },
		};
	}

	/**
	 * Force reset the circuit breaker
	 */
	reset() {
		this.state = STATES.CLOSED;
		this.failureCount = 0;
		this.successCount = 0;
		this.lastFailureTime = null;
		this.nextAttemptTime = null;
		console.log('[CircuitBreaker] Circuit breaker reset');
	}

	/**
	 * Check if the circuit breaker is open
	 */
	isOpen() {
		return this.state === STATES.OPEN && Date.now() < this.nextAttemptTime;
	}

	/**
	 * Get failure rate
	 */
	getFailureRate() {
		const total = this.stats.totalRequests;
		return total > 0 ? this.stats.totalFailures / total : 0;
	}
}

/**
 * Global circuit breakers for different services
 */
const circuitBreakers = new Map();

/**
 * Get or create a circuit breaker for a service
 * @param {string} serviceName - Name of the service
 * @param {object} options - Circuit breaker options
 * @returns {CircuitBreaker} Circuit breaker instance
 */
export function getCircuitBreaker(serviceName, options = {}) {
	if (!circuitBreakers.has(serviceName)) {
		circuitBreakers.set(serviceName, new CircuitBreaker(options));
	}
	return circuitBreakers.get(serviceName);
}

/**
 * Execute a function with circuit breaker protection
 * @param {string} serviceName - Name of the service
 * @param {Function} fn - Function to execute
 * @param {object} options - Circuit breaker options
 * @returns {Promise} Result of the function
 */
export async function executeWithCircuitBreaker(serviceName, fn, options = {}) {
	const circuitBreaker = getCircuitBreaker(serviceName, options);
	return circuitBreaker.execute(fn);
}

/**
 * Get status of all circuit breakers
 */
export function getAllCircuitBreakerStatus() {
	const status = {};
	for (const [serviceName, breaker] of circuitBreakers.entries()) {
		status[serviceName] = breaker.getStatus();
	}
	return status;
}

/**
 * Reset all circuit breakers
 */
export function resetAllCircuitBreakers() {
	for (const breaker of circuitBreakers.values()) {
		breaker.reset();
	}
	console.log('[CircuitBreaker] All circuit breakers reset');
}

/**
 * Predefined circuit breaker configurations for different services
 */
export const CIRCUIT_BREAKER_CONFIGS = {
	GEMINI_API: {
		failureThreshold: 3,
		recoveryTimeout: 30000, // 30 seconds
		monitoringPeriod: 5000, // 5 seconds
	},
	FAST_PROVIDERS: {
		failureThreshold: 2,
		recoveryTimeout: 15000, // 15 seconds
		monitoringPeriod: 3000, // 3 seconds
	},
	EMBEDDING_SERVICE: {
		failureThreshold: 3,
		recoveryTimeout: 45000, // 45 seconds
		monitoringPeriod: 10000, // 10 seconds
	},
	REDIS: {
		failureThreshold: 5,
		recoveryTimeout: 10000, // 10 seconds
		monitoringPeriod: 2000, // 2 seconds
	},
};
