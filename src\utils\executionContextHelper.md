# ExecutionContext Helper

This utility provides a compatibility layer for ExecutionContext across different environments.

## Function: waitUntil

Provides a cross-platform implementation of waitUntil functionality:

```javascript
/**
 * Provides a compatibility layer for ExecutionContext across different environments.
 * @param {object} context - The Hono context object
 * @param {Promise} promise - The promise to wait for
 */
export function waitUntil(context, promise) {
  // Check if we're in a Cloudflare Workers environment
  if (context.executionCtx && typeof context.executionCtx.waitUntil === 'function') {
    // Use the native Cloudflare Workers waitUntil
    context.executionCtx.waitUntil(promise);
  } else {
    // Fallback for Node.js/Vercel environment
    // In Node.js, we can just let the promise run without blocking the response
    // We catch errors to prevent unhandled promise rejections
    promise.catch((error) => {
      console.error('Error in background task:', error);
    });
  }
}
```

## Usage

Instead of using `c.executionCtx.waitUntil(promise)`, use:

```javascript
import { waitUntil } from '../utils/executionContextHelper.js';

// Replace:
// c.executionCtx.waitUntil(promise);

// With:
waitUntil(c, promise);