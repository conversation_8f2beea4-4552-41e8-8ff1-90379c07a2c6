/**
 * Parses the incoming request body as JSON.
 * @param {Request} request - The incoming request object.
 * @returns {Promise<object>} The parsed JSON data.
 */
import { sendTelegramError } from '../chat/telegram/index.js';

export async function parseWebhookData(request) {
	try {
		const data = await request.json();
		if (!data || typeof data !== 'object') {
			throw new Error('Invalid JSON data');
		}
		return data;
	} catch (error) {
		console.error('Failed to parse webhook data:', error);
		throw error;
	}
}

/**
 * Basic validation to check if the webhook data contains a message or callback_query.
 * @param {object} webhookData - The parsed webhook data.
 * @returns {boolean} True if the data is considered valid for processing, false otherwise.
 */
export function isValidWebhookData(webhookData) {
	if (!webhookData || typeof webhookData !== 'object') {
		return false;
	}

	const hasMessage = !!webhookData.message;
	const hasCallback = !!webhookData.callback_query;

	if (!hasMessage && !hasCallback) {
		return false;
	}

	// Additional validation for message structure
	if (hasMessage && (!webhookData.message.chat || !webhookData.message.chat.id)) {
		return false;
	}

	return true;
}

/**
 * Extracts relevant message details from the webhook data.
 * @param {object} webhookData - The parsed webhook data.
 * @returns {{chatId: number | undefined, text: string, photo: Array<object>, document: object, messageDataForLogging: object | undefined}} Extracted details.
 */
// Cache for mention regex per bot username to avoid recompilation per request
const __mentionRegexCache = new Map();

export function extractMessageDetails(env, webhookData) {
	// Validate input
	if (!isValidWebhookData(webhookData)) {
		throw new Error('Invalid webhook data structure');
	}

	const message = webhookData.message || webhookData.callback_query?.message;
	const chatId = message?.chat?.id;

	// Early return if critical data is missing
	if (!chatId) {
		throw new Error('Missing chat ID in message data');
	}

	let text = message?.text || '';
	const photo = Array.isArray(message?.photo) ? message.photo : [];
	const document = message?.document || {};

	// Use caption if media is present
	if ((photo.length > 0 && message?.caption) || document.file_id) {
		text = message?.caption || text;
	}

	// Sanitize text input
	const botUsername = env.TELEGRAM_BOT_USERNAME || '';
	if (botUsername) {
		let mentionRegex = __mentionRegexCache.get(botUsername);
		if (!mentionRegex) {
			mentionRegex = new RegExp(`@${escapeRegExp(botUsername)}\\b`, 'gi');
			__mentionRegexCache.set(botUsername, mentionRegex);
		}
		text = text.replace(mentionRegex, '').trim();
	}

	const messageDataForLogging = {
		text,
		photo: photo.length > 0 ? photo[photo.length - 1] : {},
		document,
		media_group_id: message?.media_group_id || '',
		from: webhookData.message?.from || webhookData.callback_query?.from || {},
		date: webhookData.message?.date || Math.floor(Date.now() / 1000),
	};

	return {
		chatId,
		text,
		photo,
		document,
		messageDataForLogging,
		userId: messageDataForLogging.from.id,
		username: messageDataForLogging.from.username,
	};
}

// Helper function for regex escaping
function escapeRegExp(string) {
	return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Handles initial errors occurring before background processing is scheduled.
 * Attempts to send a Telegram error notification.
 * @param {object} c - The Hono context object.
 * @param {Error} error - The error that occurred.
 */
export async function handleInitialError(c, error) {
	console.error('Error in main Telegram webhook handler (before background processing):', error);

	// Try to send error notification via Telegram if available
	if (c.env && typeof c.env.TELEGRAM_BOT_TOKEN !== 'undefined') {
		const context = {
			path: '/hrmny',
			method: 'POST',
		};

		try {
			// Use waitUntil to ensure the notification is sent even if the main handler finishes
			c.executionCtx.waitUntil(sendTelegramError(c.env, error, context));
		} catch (notificationError) {
			console.error('Failed to send error notification:', notificationError);
		}
	}
}
