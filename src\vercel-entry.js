import worker from './index.js';

// Vercel expects a default export function for serverless functions
// This adapts the Cloudflare Worker format to Vercel's format
export default async function handler(request, response) {
  // Convert Vercel's request to a Fetch API Request object
  const url = new URL(request.url, 'http://localhost');
  
  // Handle the request body properly for Node.js runtime
  let body = null;
  if (request.body) {
    if (typeof request.body === 'string') {
      body = request.body;
    } else if (Buffer.isBuffer(request.body)) {
      body = request.body;
    } else if (typeof request.body === 'object') {
      body = JSON.stringify(request.body);
    } else {
      body = request.body;
    }
  }
  
  // Create headers object, ensuring we don't duplicate headers
  const headers = new Headers();
  for (const [key, value] of Object.entries(request.headers)) {
    if (key.toLowerCase() !== 'content-length') {
      headers.append(key, value);
    }
  }
  
  const fetchRequest = new Request(url, {
    method: request.method,
    headers: headers,
    body: body,
  });

  // Create a mock env object that maps Vercel's process.env to Cloudflare Workers style
  // This allows the application to access environment variables as it expects
  const env = process.env;

  // Use the Hono app's fetch method to handle the request
  // The worker object has a fetch property that is the actual function
  // Pass the env object as the second parameter (bindings)
  const fetchResponse = await worker.fetch(fetchRequest, env);

  // Get the response body as a Buffer or string
  let responseBody;
  if (fetchResponse.body) {
    const arrayBuffer = await fetchResponse.arrayBuffer();
    responseBody = Buffer.from(arrayBuffer);
  }
  
  // Set the status code and headers
  response.status(fetchResponse.status);
  for (const [key, value] of fetchResponse.headers.entries()) {
    response.setHeader(key, value);
  }
  
  // Send the response body
  if (responseBody) {
    response.send(responseBody);
  } else {
    response.end();
  }
}