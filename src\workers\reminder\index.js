import { TaskManager } from '../../chat/tasks/taskManager.js';
import { getRedisClient } from '../../redis/redisClient.js';
import { processUserReminders } from './userProcessor.js';

/**
 * Enhanced Reminder Worker with intelligent scheduling and user preferences
 *
 * This worker handles scheduled task reminders with the following features:
 * - Processes reminders based on user preferences (timezone, quiet hours, enabled days)
 * - Groups tasks by priority for targeted messaging
 * - Limits reminders per day to avoid notification fatigue
 * - Sends rich, interactive messages with inline keyboards
 * - Handles errors gracefully with comprehensive logging
 * - Processes users in batches to optimize performance
 *
 * The worker is designed to run as a scheduled Cloudflare Worker, triggered by a CRON job.
 * It integrates with the TaskManager to retrieve tasks and user preferences from Redis,
 * and uses the Telegram API to send reminder messages to users.
 *
 * @example
 * // The worker is triggered automatically by a scheduled event
 * export default {
 *   async scheduled(controller, env, ctx) {
 *     ctx.waitUntil(this.processReminders(env));
 *   },
 *   // ... other methods
 * };
 */
export default {
	/**
	 * Handles the scheduled event (CRON trigger)
	 *
	 * This method is called by the Cloudflare Workers runtime when the scheduled event fires.
	 * It initiates the reminder processing workflow and ensures it runs to completion
	 * even if the request completes, using ctx.waitUntil() to keep the worker alive.
	 *
	 * @param {Object} controller - The scheduled controller object from Cloudflare Workers
	 * @param {Object} env - The environment object containing bindings (e.g., KV, D1, R2)
	 * @param {Object} ctx - The execution context for the worker
	 * @returns {Promise<void>}
	 */
	async scheduled(controller, env, ctx) {
		console.log('Running enhanced reminder worker...');
		ctx.waitUntil(this.processReminders(env));
	},

	/**
	 * Main reminder processing function
	 *
	 * Orchestrates the entire reminder workflow by:
	 * 1. Initializing the Redis client and TaskManager
	 * 2. Retrieving all user IDs with tasks
	 * 3. Processing users in batches to avoid timeouts
	 * 4. Handling errors at the top level to ensure partial completion
	 *
	 * @param {Object} env - The environment object containing bindings and configuration
	 * @returns {Promise<void>}
	 */
	async processReminders(env) {
		try {
			const redisClient = getRedisClient(env);
			const taskManager = new TaskManager(redisClient);
			const currentTime = new Date();

			const userIds = await taskManager.getAllUserIdsWithTasks();
			console.log(`Processing reminders for ${userIds.length} users at ${currentTime.toISOString()}`);

			// Process users in batches to avoid overwhelming the system
			const batchSize = 10;
			// Process users in batches to avoid timeouts and rate limits
			// This ensures we don't process too many users at once, which could
			// cause the worker to exceed its execution time limit or overwhelm
			// the Redis or Telegram APIs
			for (let i = 0; i < userIds.length; i += batchSize) {
				const batch = userIds.slice(i, i + batchSize);
				await Promise.all(batch.map((userId) => processUserReminders(taskManager, userId, currentTime, env)));
			}

			console.log('Enhanced reminder worker finished successfully');
		} catch (error) {
			// Log critical errors but don't re-throw to prevent worker failure
			// This ensures that partial processing is still completed even if
			// there are issues with some users or tasks
			console.error('Critical error in enhanced reminder worker:', error);
		}
	},
};
