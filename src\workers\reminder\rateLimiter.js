/**
 * Check if user has exceeded their rate limit
 *
 * @param {Object} redisClient - Redis client for storing rate limit data
 * @param {string} userId - The ID of the user to check
 * @param {number} maxRemindersPerHour - Maximum reminders allowed per hour
 * @param {number} maxRemindersPerDay - Maximum reminders allowed per day
 * @returns {Promise<{allowed: boolean, reason: string}>} Whether user is allowed to receive reminders
 */
export async function checkRateLimit(redisClient, userId, maxRemindersPerHour, maxRemindersPerDay) {
	const hourKey = `rate_limit:hour:${userId}`;
	const dayKey = `rate_limit:day:${userId}`;

	try {
		// Get current counts
		const hourCountRaw = await redisClient.get(hourKey);
		const dayCountRaw = await redisClient.get(dayKey);

		// Explicitly convert Redis string values to numbers to avoid implicit coercion
		const hourCount = Number(hourCountRaw ?? 0);
		const dayCount = Number(dayCountRaw ?? 0);

		// Check limits
		if (dayCount >= maxRemindersPerDay) {
			return { allowed: false, reason: 'daily_limit_exceeded' };
		}

		if (hourCount >= maxRemindersPerHour) {
			return { allowed: false, reason: 'hourly_limit_exceeded' };
		}

		return { allowed: true, reason: 'within_limits' };
	} catch (error) {
		console.error(`Error checking rate limit for user ${userId}:`, error);
		// If we can't check rate limits, allow the reminder to avoid losing important notifications
		return { allowed: true, reason: 'rate_limit_check_failed' };
	}
}

/**
 * Update rate limit counters after sending a reminder
 *
 * @param {Object} redisClient - Redis client for storing rate limit data
 * @param {string} userId - The ID of the user to update
 * @returns {Promise<void>}
 */
export async function updateRateLimit(redisClient, userId) {
	const hourExpiration = 60 * 60; // 1 hour in seconds
	const dayExpiration = 24 * 60 * 60; // 24 hours in seconds

	const hourKey = `rate_limit:hour:${userId}`;
	const dayKey = `rate_limit:day:${userId}`;

	try {
		// Increment counters and set expiration
		const tx = redisClient.multi();
		tx.incr(hourKey);
		tx.expire(hourKey, hourExpiration);
		tx.incr(dayKey);
		tx.expire(dayKey, dayExpiration);
		await tx.exec();
	} catch (error) {
		console.error(`Error updating rate limit for user ${userId}:`, error);
	}
}
