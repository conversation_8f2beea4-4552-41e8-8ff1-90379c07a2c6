/**
 * Check if current time is within user's quiet hours
 *
 * Determines whether the current time falls within the user's quiet hours,
 * when reminders should be suppressed. Handles both regular and overnight
 * quiet hour ranges (e.g., 22:00-07:00).
 *
 * @param {Date} currentTime - The current time to check
 * @param {Object} preferences - User's reminder preferences
 * @param {Object} preferences.quietHours - Quiet hour settings
 * @param {string} preferences.quietHours.start - Start time in HH:MM format
 * @param {string} preferences.quietHours.end - End time in HH:MM format
 * @param {string} preferences.timezone - User's timezone
 * @returns {boolean} True if current time is within quiet hours
 */
export function isQuietHours(currentTime, preferences) {
	if (!preferences.quietHours) return false;

	const userTime = convertToUserTimezone(currentTime, preferences.timezone);
	const currentHour = userTime.getHours();
	const currentMinute = userTime.getMinutes();
	const currentTimeMinutes = currentHour * 60 + currentMinute;

	const [startHour, startMinute] = preferences.quietHours.start.split(':').map(Number);
	const [endHour, endMinute] = preferences.quietHours.end.split(':').map(Number);

	const startTimeMinutes = startHour * 60 + startMinute;
	const endTimeMinutes = endHour * 60 + endMinute;

	// Handle overnight quiet hours (e.g., 22:00 to 07:00)
	// When start time is later than end time, it means the quiet period crosses midnight
	// So we check if current time is either after start time (same day) or before end time (next day)
	if (startTimeMinutes > endTimeMinutes) {
		return currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes;
	}

	return currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes;
}

/**
 * Check if today is an enabled day for reminders
 *
 * Determines whether the current day of the week is enabled for sending
 * reminders based on user preferences. Days are represented as numbers
 * (0 = Sunday, 1 = Monday, etc.). This method considers the user's timezone.
 *
 * @param {Date} currentTime - The current time to check
 * @param {Object} preferences - User's reminder preferences
 * @param {number[]} preferences.enabledDays - Array of enabled days (0-6)
 * @returns {boolean} True if today is an enabled day for reminders
 */
export function isEnabledDay(currentTime, preferences) {
	// Convert current time to user's timezone to ensure we check the correct day
	const userTime = convertToUserTimezone(currentTime, preferences.timezone);
	const dayOfWeek = userTime.getDay(); // 0 = Sunday, 1 = Monday, etc.
	return preferences.enabledDays.includes(dayOfWeek);
}

/**
 * Convert time to user's timezone
 *
 * Converts a Date object to the user's local time based on their timezone
 * preference. If the timezone is invalid, it falls back to UTC.
 *
 * @param {Date} time - The time to convert
 * @param {string} timezone - The user's timezone (e.g., 'Asia/Bangkok')
 * @returns {Date} The time converted to the user's timezone
 */
export function convertToUserTimezone(time, timezone) {
	try {
		// Use Intl.DateTimeFormat for more reliable timezone conversion
		const formatter = new Intl.DateTimeFormat('en-US', {
			timeZone: timezone,
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: false,
		});

		const parts = formatter.formatToParts(time);
		const year = parts.find((p) => p.type === 'year').value;
		const month = parts.find((p) => p.type === 'month').value;
		const day = parts.find((p) => p.type === 'day').value;
		const hour = parts.find((p) => p.type === 'hour').value;
		const minute = parts.find((p) => p.type === 'minute').value;
		const second = parts.find((p) => p.type === 'second').value;

		return new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);
	} catch {
		console.warn(`Invalid timezone ${timezone}, using UTC`);
		return time;
	}
}
