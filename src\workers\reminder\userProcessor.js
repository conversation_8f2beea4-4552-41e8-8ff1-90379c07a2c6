import { getRedisClient } from '../../redis/redisClient.js';
import { groupTasksByPriority, sendPriorityReminders } from './messageHandler.js';
import { checkRateLimit } from './rateLimiter.js';
import { isEnabledDay, isQuietHours } from './timeUtils.js';

/**
 * Process reminders for a specific user
 *
 * Handles the complete reminder workflow for a single user, including:
 * - Checking user preferences (quiet hours, enabled days)
 * - Retrieving tasks that need reminders
 * - Rate limiting to prevent spam
 * - Limiting reminders per day
 * - Grouping tasks by priority
 * - Sending appropriate reminder messages
 * - Updating task reminder status
 *
 * @param {TaskManager} taskManager - The TaskManager instance for data access
 * @param {string} userId - The ID of the user to process
 * @param {Date} currentTime - The current time for comparison
 * @param {Object} env - The environment object containing configuration and bindings
 * @returns {Promise<void>}
 */
export async function processUserReminders(taskManager, userId, currentTime, env) {
	try {
		const preferences = await taskManager.getUserReminderPreferences(userId);

		// Initialize Redis client for rate limiting
		const redisClient = getRedisClient(env);

		// Check if current time is within user's quiet hours
		if (isQuietHours(currentTime, preferences)) {
			console.log(`Skipping reminders for user ${userId} - quiet hours`);
			return;
		}

		// Check if today is an enabled day for this user
		if (!isEnabledDay(currentTime, preferences)) {
			console.log(`Skipping reminders for user ${userId} - disabled day`);
			return;
		}

		// Check rate limits
		const rateLimitCheck = await checkRateLimit(
			redisClient,
			userId,
			10, // Default max reminders per hour
			20, // Default max reminders per day
		);

		if (!rateLimitCheck.allowed) {
			console.log(`Skipping reminders for user ${userId} - rate limit exceeded (${rateLimitCheck.reason})`);
			return;
		}

		const tasksNeedingReminders = await taskManager.getTasksNeedingReminders(userId, currentTime);

		if (tasksNeedingReminders.length === 0) {
			return;
		}

		// Limit reminders per day by filtering out tasks that already had a reminder today
		// This prevents sending multiple reminders for the same task in a single day
		// while still allowing reminders for new tasks that haven't been reminded about today
		const todayReminders = tasksNeedingReminders.filter((task) => {
			const lastReminder = task.reminderSettings?.lastReminderAt;
			if (!lastReminder) return true;

			const lastReminderDate = new Date(lastReminder);
			const today = new Date();
			return lastReminderDate.toDateString() !== today.toDateString();
		});

		const remindersToSend = todayReminders.slice(0, preferences.maxRemindersPerDay);

		// Group tasks by priority for better messaging
		const groupedTasks = groupTasksByPriority(remindersToSend);

		// Send reminders based on priority
		for (const [priority, tasks] of Object.entries(groupedTasks)) {
			if (tasks.length > 0) {
				await sendPriorityReminders(taskManager, userId, priority, tasks, env, redisClient);
			}
		}
	} catch (error) {
		console.error(`Error processing reminders for user ${userId}:`, error);
	}
}
